#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特浏览器连接诊断工具

这个工具用于诊断和修复比特浏览器API连接问题。

功能：
- 自动检测比特浏览器API端口
- 测试API连接状态
- 提供修复建议
- 显示详细的错误信息

使用方法：
1. 确保比特浏览器客户端正在运行
2. 运行此脚本进行诊断
3. 根据提示进行修复

作者: AI助手
日期: 2025-01-02
"""

import requests
import json
import time
import socket
from urllib.parse import urlparse

class BitBrowserDiagnostic:
    def __init__(self):
        # 常见的比特浏览器端口
        self.common_ports = [
            "54345",  # 默认端口
            "54346", 
            "54347", 
            "54348", 
            "54349",
            "54357",  # 有些版本使用这个端口
            "9222",   # Chrome调试端口
            "9223",
            "9224"
        ]
        
        self.headers = {'Content-Type': 'application/json'}
        self.found_ports = []
        
    def print_header(self):
        """打印程序头部"""
        print("=" * 60)
        print("比特浏览器连接诊断工具")
        print("=" * 60)
        print("正在诊断比特浏览器API连接问题...")
        print("-" * 60)
    
    def check_port_open(self, host, port):
        """检查端口是否开放"""
        try:
            sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            sock.settimeout(2)
            result = sock.connect_ex((host, int(port)))
            sock.close()
            return result == 0
        except:
            return False
    
    def test_api_endpoint(self, url, endpoint="/browser/list"):
        """测试API端点"""
        try:
            response = requests.get(f"{url}{endpoint}", timeout=5)
            return {
                'success': True,
                'status_code': response.status_code,
                'response': response.text[:200] if response.text else "Empty response"
            }
        except requests.exceptions.Timeout:
            return {
                'success': False,
                'error': 'Timeout',
                'message': '连接超时'
            }
        except requests.exceptions.ConnectionError:
            return {
                'success': False,
                'error': 'ConnectionError',
                'message': '连接被拒绝'
            }
        except Exception as e:
            return {
                'success': False,
                'error': type(e).__name__,
                'message': str(e)
            }
    
    def scan_ports(self):
        """扫描比特浏览器端口"""
        print("🔍 正在扫描比特浏览器API端口...")
        print("-" * 40)
        
        for port in self.common_ports:
            print(f"检查端口 {port}...", end=" ")
            
            # 首先检查端口是否开放
            if not self.check_port_open("127.0.0.1", port):
                print("❌ 端口未开放")
                continue
            
            print("✅ 端口开放", end=" -> ")
            
            # 测试API端点
            url = f"http://127.0.0.1:{port}"
            result = self.test_api_endpoint(url)
            
            if result['success']:
                if result['status_code'] == 200:
                    print("✅ API正常")
                    self.found_ports.append({
                        'port': port,
                        'url': url,
                        'status': 'working',
                        'status_code': result['status_code']
                    })
                else:
                    print(f"⚠️  HTTP {result['status_code']}")
                    self.found_ports.append({
                        'port': port,
                        'url': url,
                        'status': 'http_error',
                        'status_code': result['status_code']
                    })
            else:
                print(f"❌ {result['message']}")
                self.found_ports.append({
                    'port': port,
                    'url': url,
                    'status': 'error',
                    'error': result['error'],
                    'message': result['message']
                })
    
    def test_browser_operations(self, url):
        """测试浏览器操作"""
        print(f"\n🧪 测试浏览器操作 ({url})...")
        print("-" * 40)
        
        # 测试获取浏览器列表
        print("1. 测试获取浏览器列表...", end=" ")
        result = self.test_api_endpoint(url, "/browser/list")
        if result['success'] and result['status_code'] == 200:
            print("✅ 成功")
            try:
                data = json.loads(result['response'])
                if 'data' in data and isinstance(data['data'], list):
                    print(f"   找到 {len(data['data'])} 个浏览器窗口")
                    if len(data['data']) > 0:
                        print("   浏览器窗口示例:")
                        for i, browser in enumerate(data['data'][:3]):  # 显示前3个
                            browser_id = browser.get('id', 'N/A')
                            browser_name = browser.get('name', 'N/A')
                            print(f"     {i+1}. ID: {browser_id[:20]}... 名称: {browser_name}")
                else:
                    print("   响应格式异常")
            except:
                print("   响应解析失败")
        else:
            print(f"❌ 失败 ({result.get('message', 'Unknown error')})")
        
        # 测试创建浏览器窗口
        print("2. 测试创建浏览器窗口...", end=" ")
        test_data = {
            'name': 'DiagnosticTest',
            'remark': 'Diagnostic test browser',
            'proxyMethod': 2,
            'proxyType': 'noproxy',
            'host': '',
            'port': '',
            'proxyUserName': '',
            "browserFingerPrint": {
                'coreVersion': '124'
            }
        }
        
        try:
            response = requests.post(
                f"{url}/browser/update",
                data=json.dumps(test_data),
                headers=self.headers,
                timeout=10
            )
            
            if response.status_code == 200:
                result_data = response.json()
                if result_data.get('success'):
                    browser_id = result_data['data']['id']
                    print("✅ 成功")
                    print(f"   创建的浏览器ID: {browser_id}")
                    
                    # 尝试删除测试浏览器
                    print("3. 清理测试浏览器...", end=" ")
                    delete_data = {'id': browser_id}
                    delete_response = requests.post(
                        f"{url}/browser/delete",
                        data=json.dumps(delete_data),
                        headers=self.headers,
                        timeout=10
                    )
                    if delete_response.status_code == 200:
                        print("✅ 成功")
                    else:
                        print(f"⚠️  删除失败 (HTTP {delete_response.status_code})")
                else:
                    error_msg = result_data.get('msg', '未知错误')
                    print(f"❌ 失败: {error_msg}")
            else:
                print(f"❌ 失败 (HTTP {response.status_code})")
                
        except Exception as e:
            print(f"❌ 失败: {e}")
    
    def show_results(self):
        """显示诊断结果"""
        print("\n" + "=" * 60)
        print("诊断结果")
        print("=" * 60)
        
        if not self.found_ports:
            print("❌ 未找到任何可用的比特浏览器API端口")
            print("\n💡 解决建议:")
            print("1. 确保比特浏览器客户端正在运行")
            print("2. 检查比特浏览器设置中的API端口配置")
            print("3. 尝试重启比特浏览器客户端")
            print("4. 检查防火墙是否阻止了相关端口")
            return
        
        working_ports = [p for p in self.found_ports if p['status'] == 'working']
        error_ports = [p for p in self.found_ports if p['status'] != 'working']
        
        if working_ports:
            print("✅ 找到可用的API端口:")
            for port_info in working_ports:
                print(f"   🌐 {port_info['url']} (端口 {port_info['port']})")
            
            # 测试第一个可用端口的详细功能
            best_port = working_ports[0]
            self.test_browser_operations(best_port['url'])
            
            print(f"\n🎯 推荐使用: {best_port['url']}")
            print("   请在您的工具中使用此API地址")
            
        if error_ports:
            print(f"\n⚠️  发现 {len(error_ports)} 个有问题的端口:")
            for port_info in error_ports:
                status_msg = ""
                if port_info['status'] == 'http_error':
                    status_msg = f"HTTP {port_info['status_code']}"
                else:
                    status_msg = port_info.get('message', '未知错误')
                print(f"   ❌ 端口 {port_info['port']}: {status_msg}")
    
    def show_fix_suggestions(self):
        """显示修复建议"""
        print("\n" + "=" * 60)
        print("修复建议")
        print("=" * 60)
        
        print("如果仍然遇到问题，请尝试以下解决方案:")
        print()
        print("1. 🔄 重启比特浏览器")
        print("   - 完全关闭比特浏览器客户端")
        print("   - 等待5秒后重新启动")
        print("   - 确保客户端完全加载完成")
        print()
        print("2. 🔧 检查比特浏览器设置")
        print("   - 打开比特浏览器设置")
        print("   - 查看API服务配置")
        print("   - 确认API服务已启用")
        print()
        print("3. 🛡️ 检查防火墙和安全软件")
        print("   - 确保防火墙允许比特浏览器通信")
        print("   - 检查杀毒软件是否阻止了API服务")
        print()
        print("4. 🔍 手动检查进程")
        print("   - 打开任务管理器")
        print("   - 查找比特浏览器相关进程")
        print("   - 确认进程正在运行")
        print()
        print("5. 📞 联系技术支持")
        print("   - 如果以上方法都无效")
        print("   - 请联系比特浏览器技术支持")
        print("   - 提供此诊断报告的结果")
    
    def run(self):
        """运行诊断"""
        self.print_header()
        
        # 扫描端口
        self.scan_ports()
        
        # 显示结果
        self.show_results()
        
        # 显示修复建议
        self.show_fix_suggestions()
        
        print("\n" + "=" * 60)
        print("诊断完成")
        print("=" * 60)

def main():
    """主函数"""
    diagnostic = BitBrowserDiagnostic()
    try:
        diagnostic.run()
    except KeyboardInterrupt:
        print("\n\n诊断被用户中断")
    except Exception as e:
        print(f"\n诊断过程中出现错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
