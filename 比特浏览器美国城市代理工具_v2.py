#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特浏览器美国城市代理工具 v2.0
- 重新设计的界面布局
- 配置窗口独立
- 集成城市精准性测试
- 自动更换IP功能
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext, simpledialog
import requests
import json
import time
import re
import threading
import random
from threading import Thread, Timer
from concurrent.futures import ThreadPoolExecutor, as_completed
from verified_cities import VERIFIED_US_CITIES

# 比特浏览器风格颜色配置
class BitBrowserColors:
    PRIMARY = "#4A90E2"      # 主蓝色
    PRIMARY_DARK = "#357ABD"  # 深蓝色
    BACKGROUND = "#F8F9FA"    # 背景灰
    CARD_BG = "#FFFFFF"       # 卡片背景
    TEXT_PRIMARY = "#333333"  # 主文字
    TEXT_SECONDARY = "#6C757D" # 次要文字
    SUCCESS = "#28A745"       # 成功绿
    WARNING = "#FFC107"       # 警告黄
    DANGER = "#DC3545"        # 危险红
    BORDER = "#DEE2E6"        # 边框色

class ConfigWindow:
    """配置窗口类"""
    def __init__(self, parent, config_data):
        self.parent = parent
        self.config_data = config_data.copy()
        self.result = None
        
        # 创建配置窗口
        self.window = tk.Toplevel(parent)
        self.window.title("⚙️ 系统配置")
        self.window.geometry("500x600")
        self.window.resizable(False, False)
        self.window.transient(parent)
        self.window.grab_set()
        
        # 居中显示
        self.center_window()
        
        # 创建界面
        self.create_widgets()
        
    def center_window(self):
        """窗口居中"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        x = (self.window.winfo_screenwidth() // 2) - (width // 2)
        y = (self.window.winfo_screenheight() // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")
        
    def create_widgets(self):
        """创建配置界面组件"""
        # 主框架
        main_frame = ttk.Frame(self.window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)
        
        # Oxylabs代理配置
        oxylabs_frame = ttk.LabelFrame(main_frame, text="🔐 Oxylabs代理配置", padding="15")
        oxylabs_frame.pack(fill=tk.X, pady=(0, 15))
        
        # 账户信息
        ttk.Label(oxylabs_frame, text="👤 用户名:", font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 10))
        self.username_var = tk.StringVar(value=self.config_data.get('username', 'z123456_bYIud'))
        username_entry = ttk.Entry(oxylabs_frame, textvariable=self.username_var, width=30, font=("Microsoft YaHei", 9))
        username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(oxylabs_frame, text="🔑 密码:", font=("Microsoft YaHei", 9)).grid(row=1, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 10))
        self.password_var = tk.StringVar(value=self.config_data.get('password', 'Zzr442859970~'))
        password_entry = ttk.Entry(oxylabs_frame, textvariable=self.password_var, show="*", width=30, font=("Microsoft YaHei", 9))
        password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(oxylabs_frame, text="🌐 代理主机:", font=("Microsoft YaHei", 9)).grid(row=2, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 10))
        self.proxy_host_var = tk.StringVar(value=self.config_data.get('proxy_host', 'pr.oxylabs.io'))
        host_entry = ttk.Entry(oxylabs_frame, textvariable=self.proxy_host_var, width=30, font=("Microsoft YaHei", 9))
        host_entry.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(oxylabs_frame, text="🔌 代理端口:", font=("Microsoft YaHei", 9)).grid(row=3, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 10))
        self.proxy_port_var = tk.StringVar(value=self.config_data.get('proxy_port', '7777'))
        port_entry = ttk.Entry(oxylabs_frame, textvariable=self.proxy_port_var, width=30, font=("Microsoft YaHei", 9))
        port_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        oxylabs_frame.columnconfigure(1, weight=1)
        
        # 会话时间设置
        session_frame = ttk.LabelFrame(main_frame, text="⏰ 会话时间设置", padding="15")
        session_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(session_frame, text="会话时间:", font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10))
        self.session_time_var = tk.IntVar(value=self.config_data.get('session_time', 30))
        session_spin = ttk.Spinbox(session_frame, from_=5, to=1440, textvariable=self.session_time_var, 
                                 width=10, font=("Microsoft YaHei", 9))
        session_spin.grid(row=0, column=1, sticky=tk.W, padx=(0, 10))
        ttk.Label(session_frame, text="分钟 (5-1440)", font=("Microsoft YaHei", 8), foreground="gray").grid(row=0, column=2, sticky=tk.W)
        
        # BitBrowser配置
        browser_frame = ttk.LabelFrame(main_frame, text="🌐 BitBrowser配置", padding="15")
        browser_frame.pack(fill=tk.X, pady=(0, 15))
        
        ttk.Label(browser_frame, text="API端口:", font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 10), pady=(0, 10))
        self.api_port_var = tk.StringVar(value=self.config_data.get('api_port', '54345'))
        port_entry = ttk.Entry(browser_frame, textvariable=self.api_port_var, width=30, font=("Microsoft YaHei", 9))
        port_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=(0, 10))
        
        ttk.Label(browser_frame, text="浏览器名称:", font=("Microsoft YaHei", 9)).grid(row=1, column=0, sticky=tk.W, padx=(0, 10))
        self.browser_name_var = tk.StringVar(value=self.config_data.get('browser_name', 'US_City_Browser'))
        name_entry = ttk.Entry(browser_frame, textvariable=self.browser_name_var, width=30, font=("Microsoft YaHei", 9))
        name_entry.grid(row=1, column=1, sticky=(tk.W, tk.E))
        
        browser_frame.columnconfigure(1, weight=1)
        
        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X, pady=(20, 0))
        
        ttk.Button(btn_frame, text="💾 保存", command=self.save_config, 
                  style="Primary.TButton", width=12).pack(side=tk.RIGHT, padx=(10, 0))
        ttk.Button(btn_frame, text="❌ 取消", command=self.cancel, 
                  style="Warning.TButton", width=12).pack(side=tk.RIGHT)
        
    def save_config(self):
        """保存配置"""
        self.result = {
            'username': self.username_var.get(),
            'password': self.password_var.get(),
            'proxy_host': self.proxy_host_var.get(),
            'proxy_port': self.proxy_port_var.get(),
            'session_time': self.session_time_var.get(),
            'api_port': self.api_port_var.get(),
            'browser_name': self.browser_name_var.get()
        }
        self.window.destroy()
        
    def cancel(self):
        """取消配置"""
        self.result = None
        self.window.destroy()

class BitBrowserUSCityTool:
    """比特浏览器美国城市代理工具主类"""
    def __init__(self, root):
        self.root = root
        self.root.title("比特浏览器美国城市代理工具 v2.0")
        self.root.geometry("1000x700")
        self.root.configure(bg=BitBrowserColors.BACKGROUND)
        
        # 初始化变量
        self.init_variables()
        
        # 配置TTK样式
        self.configure_styles()
        
        # 创建界面
        self.create_widgets()
        
        # 加载设置
        self.load_settings()
        
        # 初始化城市列表
        self.update_city_list()
        
    def init_variables(self):
        """初始化变量"""
        # 基础配置
        self.config = {
            'username': 'z123456_bYIud',
            'password': 'Zzr442859970~',
            'proxy_host': 'pr.oxylabs.io',
            'proxy_port': '7777',
            'session_time': 30,
            'api_port': '54345',
            'browser_name': 'US_City_Browser'
        }
        
        # 界面变量
        self.selected_city = None
        self.selected_city_en = None
        self.browser_id_var = tk.StringVar()
        self.search_var = tk.StringVar()
        self.filtered_cities = []
        
        # 自动更换IP相关变量
        self.auto_change_enabled = False
        self.auto_change_interval = 30
        self.auto_change_timer = None
        self.countdown_timer = None
        self.remaining_time = 0
        
        # 城市测试相关变量
        self.test_results = []
        self.test_in_progress = False
        
        # 城市列表
        self.us_cities = VERIFIED_US_CITIES
        
    def configure_styles(self):
        """配置TTK样式"""
        style = ttk.Style()
        
        # 配置主题
        style.theme_use('clam')
        
        # 配置颜色
        style.configure("TLabelFrame", 
                       background=BitBrowserColors.CARD_BG,
                       borderwidth=1,
                       relief="solid")
        
        style.configure("TLabelFrame.Label", 
                       font=("Microsoft YaHei", 11, "bold"),
                       foreground=BitBrowserColors.PRIMARY,
                       background=BitBrowserColors.CARD_BG)
        
        # 按钮样式
        style.configure("Primary.TButton",
                       background=BitBrowserColors.PRIMARY,
                       foreground="white",
                       font=("Microsoft YaHei", 9, "bold"),
                       borderwidth=0,
                       focuscolor="none")
        
        style.configure("Success.TButton",
                       background=BitBrowserColors.SUCCESS,
                       foreground="white",
                       font=("Microsoft YaHei", 9, "bold"),
                       borderwidth=0,
                       focuscolor="none")
        
        style.configure("Warning.TButton",
                       background=BitBrowserColors.WARNING,
                       foreground="white",
                       font=("Microsoft YaHei", 9, "bold"),
                       borderwidth=0,
                       focuscolor="none")
        
    def create_widgets(self):
        """创建主界面组件"""
        # 标题区域
        title_frame = tk.Frame(self.root, bg=BitBrowserColors.PRIMARY, height=60)
        title_frame.pack(fill=tk.X)
        title_frame.pack_propagate(False)
        
        title_label = tk.Label(title_frame, text="🌍 比特浏览器美国城市代理工具 v2.0", 
                              font=("Microsoft YaHei", 16, "bold"), 
                              fg="white", bg=BitBrowserColors.PRIMARY)
        title_label.pack(expand=True)
        
        # 分隔线
        separator1 = ttk.Separator(self.root, orient='horizontal')
        separator1.pack(fill=tk.X, padx=20, pady=(10, 0))
        
        # 创建主要内容区域（左右分栏）
        main_frame = tk.Frame(self.root, bg=BitBrowserColors.BACKGROUND)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        # 左侧操作区域
        left_frame = ttk.LabelFrame(main_frame, text="🎯 操作控制", padding="20")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))
        
        # 右侧城市选择区域
        right_frame = ttk.LabelFrame(main_frame, text="🌍 城市选择", padding="20")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))
        
        # 分隔线
        separator2 = ttk.Separator(self.root, orient='horizontal')
        separator2.pack(fill=tk.X, padx=20, pady=(15, 10))
        
        # 底部日志区域
        log_frame = ttk.LabelFrame(self.root, text="📋 操作日志", padding="15")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(5, 20))
        
        # 创建各区域内容
        self.create_operation_area(left_frame)
        self.create_city_area(right_frame)
        self.create_log_area(log_frame)

    def create_operation_area(self, parent):
        """创建左侧操作区域"""
        # 基础操作按钮
        basic_frame = ttk.LabelFrame(parent, text="🚀 基础操作", padding="15")
        basic_frame.pack(fill=tk.X, pady=(0, 15))

        # 第一行按钮
        btn_row1 = ttk.Frame(basic_frame)
        btn_row1.pack(fill=tk.X, pady=(0, 10))

        self.create_btn = ttk.Button(btn_row1, text="🔄 修改当前IP",
                                   command=self.create_or_update_browser,
                                   style="Primary.TButton", width=18, state=tk.DISABLED)
        self.create_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.open_btn = ttk.Button(btn_row1, text="🌐 打开浏览器",
                                 command=self.open_browser, state=tk.DISABLED,
                                 style="Success.TButton", width=18)
        self.open_btn.pack(side=tk.LEFT)

        # 第二行按钮
        btn_row2 = ttk.Frame(basic_frame)
        btn_row2.pack(fill=tk.X, pady=(0, 10))

        self.close_btn = ttk.Button(btn_row2, text="❌ 关闭浏览器",
                                  command=self.close_browser, state=tk.DISABLED,
                                  style="Warning.TButton", width=18)
        self.close_btn.pack(side=tk.LEFT, padx=(0, 10))

        config_btn = ttk.Button(btn_row2, text="⚙️ 配置设置",
                              command=self.open_config_window,
                              style="Primary.TButton", width=18)
        config_btn.pack(side=tk.LEFT)

        # 浏览器ID设置
        id_frame = ttk.Frame(basic_frame)
        id_frame.pack(fill=tk.X)

        ttk.Label(id_frame, text="浏览器ID:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        id_entry = ttk.Entry(id_frame, textvariable=self.browser_id_var, width=25, font=("Microsoft YaHei", 9))
        id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))
        ttk.Label(id_frame, text="(留空自动创建)", font=("Microsoft YaHei", 8), foreground="gray").pack(side=tk.LEFT)

        # 自动更换IP控制
        auto_frame = ttk.LabelFrame(parent, text="🔄 自动更换IP", padding="15")
        auto_frame.pack(fill=tk.X, pady=(0, 15))

        # 启用开关和间隔设置
        control_frame = ttk.Frame(auto_frame)
        control_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(control_frame, text="启用:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.auto_change_var = tk.BooleanVar(value=False)
        auto_check = ttk.Checkbutton(control_frame, variable=self.auto_change_var)
        auto_check.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(control_frame, text="间隔:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.interval_var = tk.IntVar(value=self.auto_change_interval)
        interval_spin = ttk.Spinbox(control_frame, from_=5, to=120, textvariable=self.interval_var,
                                  width=8, font=("Microsoft YaHei", 9))
        interval_spin.pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(control_frame, text="分钟", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)

        # 状态显示
        status_frame = ttk.Frame(auto_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="状态:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.auto_status_label = ttk.Label(status_frame, text="❌ 已停止",
                                         font=("Microsoft YaHei", 9), foreground="red")
        self.auto_status_label.pack(side=tk.LEFT, padx=(0, 15))

        ttk.Label(status_frame, text="倒计时:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.countdown_label = ttk.Label(status_frame, text="--:--",
                                       font=("Microsoft YaHei", 9), foreground="blue")
        self.countdown_label.pack(side=tk.LEFT)

        # 控制按钮
        auto_btn_frame = ttk.Frame(auto_frame)
        auto_btn_frame.pack(fill=tk.X)

        self.start_auto_btn = ttk.Button(auto_btn_frame, text="▶️ 开始",
                                       command=self.start_auto_change,
                                       style="Success.TButton", width=12)
        self.start_auto_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.stop_auto_btn = ttk.Button(auto_btn_frame, text="⏹️ 停止",
                                      command=self.stop_auto_change,
                                      style="Warning.TButton", width=12, state=tk.DISABLED)
        self.stop_auto_btn.pack(side=tk.LEFT)

        # 城市测试功能
        test_frame = ttk.LabelFrame(parent, text="🧪 城市测试", padding="15")
        test_frame.pack(fill=tk.X)

        test_btn_frame = ttk.Frame(test_frame)
        test_btn_frame.pack(fill=tk.X, pady=(0, 10))

        self.test_cities_btn = ttk.Button(test_btn_frame, text="🧪 测试城市精准性",
                                        command=self.start_city_test,
                                        style="Primary.TButton", width=20)
        self.test_cities_btn.pack(side=tk.LEFT, padx=(0, 10))

        self.update_cities_btn = ttk.Button(test_btn_frame, text="🔄 更新城市列表",
                                          command=self.update_verified_cities,
                                          style="Success.TButton", width=20, state=tk.DISABLED)
        self.update_cities_btn.pack(side=tk.LEFT)

        # 测试进度显示
        self.test_progress_var = tk.StringVar(value="准备就绪")
        self.test_progress_label = ttk.Label(test_frame, textvariable=self.test_progress_var,
                                           font=("Microsoft YaHei", 9), foreground="blue")
        self.test_progress_label.pack(pady=(5, 0))

    def create_city_area(self, parent):
        """创建右侧城市选择区域"""
        # 搜索框
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(search_frame, text="🔍 搜索城市:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=25, font=("Microsoft YaHei", 9))
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        clear_btn = ttk.Button(search_frame, text="清除", command=self.clear_search, width=6)
        clear_btn.pack(side=tk.RIGHT)

        # 城市列表
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        self.city_listbox = tk.Listbox(list_frame, height=15, font=("Microsoft YaHei", 9),
                                      selectmode=tk.SINGLE, activestyle='dotbox')
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.city_listbox.yview)
        self.city_listbox.configure(yscrollcommand=scrollbar.set)

        self.city_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.city_listbox.bind('<<ListboxSelect>>', self.on_city_select)

        # 选中城市显示
        city_status_frame = ttk.LabelFrame(parent, text="📍 当前选择", padding="10")
        city_status_frame.pack(fill=tk.X)

        self.selected_city_label = ttk.Label(city_status_frame, text="❌ 未选择城市",
                                           font=("Microsoft YaHei", 10, "bold"), foreground="red")
        self.selected_city_label.pack()

    def create_log_area(self, parent):
        """创建日志区域"""
        self.log_text = scrolledtext.ScrolledText(parent, height=8, font=("Consolas", 9),
                                                 bg="white", fg="black", wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 添加欢迎信息
        self.log_message("🎉 比特浏览器美国城市代理工具 v2.0 已启动")
        self.log_message("📝 请先选择城市，然后配置代理设置")

    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        self.log_text.insert(tk.END, log_entry)
        self.log_text.see(tk.END)
        self.root.update_idletasks()

    def open_config_window(self):
        """打开配置窗口"""
        config_window = ConfigWindow(self.root, self.config)
        self.root.wait_window(config_window.window)

        if config_window.result:
            self.config.update(config_window.result)
            self.save_settings()
            self.log_message("✅ 配置已更新并保存")

    def update_city_list(self):
        """更新城市列表"""
        self.filtered_cities = []
        for state, cities in self.us_cities.items():
            for city_cn, city_en in cities:
                display_text = f"{city_cn} ({state})"
                self.filtered_cities.append(display_text)

        self.city_listbox.delete(0, tk.END)
        for city in sorted(self.filtered_cities):
            self.city_listbox.insert(tk.END, city)

    def on_search_change(self, *args):
        """搜索框内容变化时的回调"""
        search_term = self.search_var.get().lower()
        if search_term:
            filtered = [city for city in self.filtered_cities if search_term in city.lower()]
        else:
            filtered = self.filtered_cities

        self.city_listbox.delete(0, tk.END)
        for city in sorted(filtered):
            self.city_listbox.insert(tk.END, city)

    def clear_search(self):
        """清除搜索"""
        self.search_var.set("")

    def on_city_select(self, event):
        """城市选择回调"""
        selection = self.city_listbox.curselection()
        if selection:
            selected_text = self.city_listbox.get(selection[0])

            # 解析选中的城市
            for state, cities in self.us_cities.items():
                for city_cn, city_en in cities:
                    if f"{city_cn} ({state})" == selected_text:
                        self.selected_city = city_cn
                        self.selected_city_en = city_en
                        self.selected_city_label.config(
                            text=f"✅ {city_cn}, {state}",
                            foreground="green"
                        )

                        # 启用操作按钮
                        self.create_btn.config(state=tk.NORMAL)

                        self.log_message(f"🏙️ 已选择城市: {city_cn} ({city_en})")
                        return

    def save_settings(self):
        """保存设置到文件"""
        try:
            settings = {
                'config': self.config,
                'browser_id': self.browser_id_var.get(),
                'auto_change_enabled': self.auto_change_enabled,
                'auto_change_interval': self.auto_change_interval
            }

            with open('settings.json', 'w', encoding='utf-8') as f:
                json.dump(settings, f, ensure_ascii=False, indent=2)

        except Exception as e:
            self.log_message(f"❌ 保存设置失败: {str(e)}")

    def load_settings(self):
        """从文件加载设置"""
        try:
            with open('settings.json', 'r', encoding='utf-8') as f:
                settings = json.load(f)

            if 'config' in settings:
                self.config.update(settings['config'])

            if 'browser_id' in settings:
                self.browser_id_var.set(settings['browser_id'])

            if 'auto_change_enabled' in settings:
                self.auto_change_enabled = settings['auto_change_enabled']
                self.auto_change_var.set(self.auto_change_enabled)

            if 'auto_change_interval' in settings:
                self.auto_change_interval = settings['auto_change_interval']
                self.interval_var.set(self.auto_change_interval)

            self.log_message("✅ 设置已加载")

        except FileNotFoundError:
            self.log_message("📝 未找到设置文件，使用默认配置")
        except Exception as e:
            self.log_message(f"❌ 加载设置失败: {str(e)}")

    def create_or_update_browser(self):
        """创建或更新浏览器"""
        if not self.selected_city:
            messagebox.showwarning("警告", "请先选择一个城市")
            return

        def run_create():
            try:
                self.log_message(f"🔄 开始修改浏览器IP为 {self.selected_city}...")

                # 生成代理参数
                session_id = f"sess{random.randint(10000, 99999)}"
                city_param = f"cc-US-city-{self.selected_city_en}"
                proxy_username = f"customer-{self.config['username']}-{city_param}-sessid-{session_id}-sesstime-{self.config['session_time']}"

                # 准备API数据
                json_data = {
                    'name': self.config['browser_name'],
                    'remark': f'US City: {self.selected_city}',
                    'proxyMethod': 2,
                    'proxyType': 'http',
                    'host': self.config['proxy_host'],
                    'port': self.config['proxy_port'],
                    'proxyUserName': proxy_username,
                    'proxyPassword': self.config['password'],
                    'browserFingerPrint': {}
                }

                browser_id = self.browser_id_var.get().strip()

                if browser_id:
                    # 更新现有浏览器
                    json_data['id'] = browser_id
                    response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/update",
                                           json=json_data, timeout=60)
                else:
                    # 创建新浏览器
                    response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/create",
                                           json=json_data, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        if not browser_id:
                            new_id = result.get('data', {}).get('id')
                            if new_id:
                                self.browser_id_var.set(new_id)
                                self.log_message(f"✅ 浏览器创建成功，ID: {new_id}")
                            else:
                                self.log_message("✅ 浏览器创建成功")
                        else:
                            self.log_message(f"✅ 浏览器 {browser_id} 更新成功")

                        # 启用其他按钮
                        self.open_btn.config(state=tk.NORMAL)
                        self.close_btn.config(state=tk.NORMAL)

                        # 保存设置
                        self.save_settings()

                    else:
                        error_msg = result.get('msg', '未知错误')
                        self.log_message(f"❌ 操作失败: {error_msg}")
                        messagebox.showerror("错误", f"操作失败: {error_msg}")
                else:
                    self.log_message(f"❌ API请求失败: HTTP {response.status_code}")
                    messagebox.showerror("错误", f"API请求失败: HTTP {response.status_code}")

            except Exception as e:
                error_msg = f"操作失败: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)

        # 在后台线程中执行
        Thread(target=run_create, daemon=True).start()

    def open_browser(self):
        """打开浏览器"""
        browser_id = self.browser_id_var.get().strip()
        if not browser_id:
            messagebox.showwarning("警告", "请先创建或输入浏览器ID")
            return

        def run_open():
            try:
                self.log_message(f"🌐 正在打开浏览器 {browser_id}...")

                json_data = {'id': browser_id}
                response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/open",
                                       json=json_data, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.log_message(f"✅ 浏览器 {browser_id} 已打开")
                    else:
                        error_msg = result.get('msg', '未知错误')
                        self.log_message(f"❌ 打开失败: {error_msg}")
                        messagebox.showerror("错误", f"打开失败: {error_msg}")
                else:
                    self.log_message(f"❌ API请求失败: HTTP {response.status_code}")

            except Exception as e:
                error_msg = f"打开浏览器失败: {str(e)}"
                self.log_message(f"❌ {error_msg}")
                messagebox.showerror("错误", error_msg)

        Thread(target=run_open, daemon=True).start()

    def close_browser(self):
        """关闭浏览器"""
        browser_id = self.browser_id_var.get().strip()
        if not browser_id:
            messagebox.showwarning("警告", "请先输入浏览器ID")
            return

        def run_close():
            try:
                self.log_message(f"🔒 正在关闭浏览器 {browser_id}...")

                json_data = {'id': browser_id}
                response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/close",
                                       json=json_data, timeout=60)

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.log_message(f"✅ 浏览器 {browser_id} 已关闭")
                    else:
                        error_msg = result.get('msg', '未知错误')
                        self.log_message(f"❌ 关闭失败: {error_msg}")
                else:
                    self.log_message(f"❌ API请求失败: HTTP {response.status_code}")

            except Exception as e:
                error_msg = f"关闭浏览器失败: {str(e)}"
                self.log_message(f"❌ {error_msg}")

        Thread(target=run_close, daemon=True).start()

    def start_auto_change(self):
        """开始自动更换IP"""
        if not self.selected_city:
            messagebox.showwarning("警告", "请先选择一个城市")
            return

        browser_id = self.browser_id_var.get().strip()
        if not browser_id:
            messagebox.showwarning("警告", "请先创建或输入浏览器ID")
            return

        self.auto_change_enabled = True
        self.auto_change_interval = self.interval_var.get()

        # 更新UI状态
        self.auto_status_label.config(text="✅ 运行中", foreground="green")
        self.start_auto_btn.config(state=tk.DISABLED)
        self.stop_auto_btn.config(state=tk.NORMAL)

        self.log_message(f"🔄 自动更换IP已启动，间隔: {self.auto_change_interval}分钟")

        # 开始第一次更换
        self.schedule_next_change()

    def stop_auto_change(self):
        """停止自动更换IP"""
        self.auto_change_enabled = False

        # 取消定时器
        if self.auto_change_timer:
            self.auto_change_timer.cancel()
            self.auto_change_timer = None

        if self.countdown_timer:
            self.countdown_timer.cancel()
            self.countdown_timer = None

        # 更新UI状态
        self.auto_status_label.config(text="❌ 已停止", foreground="red")
        self.countdown_label.config(text="--:--")
        self.start_auto_btn.config(state=tk.NORMAL)
        self.stop_auto_btn.config(state=tk.DISABLED)

        self.log_message("⏹️ 自动更换IP已停止")

    def schedule_next_change(self):
        """安排下次更换"""
        if not self.auto_change_enabled:
            return

        self.remaining_time = self.auto_change_interval * 60  # 转换为秒
        self.start_countdown()

        # 安排下次更换
        self.auto_change_timer = Timer(self.remaining_time, self.execute_auto_change)
        self.auto_change_timer.start()

    def start_countdown(self):
        """开始倒计时显示"""
        if not self.auto_change_enabled or self.remaining_time <= 0:
            return

        minutes = self.remaining_time // 60
        seconds = self.remaining_time % 60
        self.countdown_label.config(text=f"{minutes:02d}:{seconds:02d}")

        self.remaining_time -= 1

        # 安排下次更新
        self.countdown_timer = Timer(1.0, self.start_countdown)
        self.countdown_timer.start()

    def execute_auto_change(self):
        """执行自动更换IP"""
        if not self.auto_change_enabled:
            return

        def run_change():
            try:
                self.log_message("🔄 执行自动更换IP...")

                # 先关闭浏览器
                self.close_browser_silent()
                time.sleep(2)

                # 更新代理配置
                self.update_browser_proxy()
                time.sleep(1)

                # 显示成功弹窗
                self.show_success_popup()

                # 安排下次更换
                if self.auto_change_enabled:
                    self.schedule_next_change()

            except Exception as e:
                self.log_message(f"❌ 自动更换IP失败: {str(e)}")
                self.stop_auto_change()

        Thread(target=run_change, daemon=True).start()

    def close_browser_silent(self):
        """静默关闭浏览器"""
        try:
            browser_id = self.browser_id_var.get().strip()
            if browser_id:
                json_data = {'id': browser_id}
                response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/close",
                                       json=json_data, timeout=30)
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.log_message("🔒 浏览器已自动关闭")
        except:
            pass  # 静默处理错误

    def update_browser_proxy(self):
        """更新浏览器代理配置"""
        try:
            browser_id = self.browser_id_var.get().strip()
            if not browser_id:
                return

            # 生成新的代理参数
            session_id = f"sess{random.randint(10000, 99999)}"
            city_param = f"cc-US-city-{self.selected_city_en}"
            proxy_username = f"customer-{self.config['username']}-{city_param}-sessid-{session_id}-sesstime-{self.config['session_time']}"

            json_data = {
                'id': browser_id,
                'name': self.config['browser_name'],
                'remark': f'US City: {self.selected_city}',
                'proxyMethod': 2,
                'proxyType': 'http',
                'host': self.config['proxy_host'],
                'port': self.config['proxy_port'],
                'proxyUserName': proxy_username,
                'proxyPassword': self.config['password'],
                'browserFingerPrint': {}
            }

            response = requests.post(f"http://127.0.0.1:{self.config['api_port']}/browser/update",
                                   json=json_data, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_message("✅ 代理配置已更新")

        except Exception as e:
            self.log_message(f"❌ 更新代理配置失败: {str(e)}")

    def show_success_popup(self):
        """显示成功弹窗"""
        def show_popup():
            popup = tk.Toplevel(self.root)
            popup.title("更换成功")
            popup.geometry("300x150")
            popup.resizable(False, False)
            popup.transient(self.root)
            popup.grab_set()

            # 居中显示
            popup.update_idletasks()
            x = (popup.winfo_screenwidth() // 2) - (popup.winfo_width() // 2)
            y = (popup.winfo_screenheight() // 2) - (popup.winfo_height() // 2)
            popup.geometry(f"300x150+{x}+{y}")

            # 内容
            frame = tk.Frame(popup, bg="white")
            frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=20)

            tk.Label(frame, text="🎉", font=("Arial", 24), bg="white").pack(pady=(10, 5))
            tk.Label(frame, text="IP更换成功！", font=("Microsoft YaHei", 12, "bold"), bg="white").pack(pady=5)
            tk.Label(frame, text="浏览器已自动关闭", font=("Microsoft YaHei", 9), bg="white", fg="gray").pack()

            # 3秒后自动关闭
            popup.after(3000, popup.destroy)

        self.root.after(0, show_popup)

    def start_city_test(self):
        """开始城市精准性测试"""
        if self.test_in_progress:
            messagebox.showinfo("提示", "测试正在进行中，请等待完成")
            return

        result = messagebox.askyesno("确认测试",
                                   "城市精准性测试将验证所有城市的IP准确性。\n"
                                   "这个过程可能需要几分钟时间。\n\n"
                                   "是否继续？")
        if not result:
            return

        self.test_in_progress = True
        self.test_cities_btn.config(state=tk.DISABLED)
        self.test_progress_var.set("🧪 准备测试...")

        def run_test():
            try:
                self.test_results = []
                successful_cities = []
                failed_cities = []

                # 测试城市列表（从测试文件复制）
                test_cities = {
                    "加利福尼亚州": [
                        ("洛杉矶", "los_angeles"),
                        ("圣地亚哥", "san_diego"),
                        ("圣何塞", "san_jose"),
                        ("旧金山", "san_francisco"),
                        ("萨克拉门托", "sacramento"),
                        ("弗雷斯诺", "fresno"),
                        ("长滩", "long_beach"),
                        ("奥克兰", "oakland")
                    ],
                    "纽约州": [
                        ("纽约", "new_york"),
                        ("布法罗", "buffalo"),
                        ("罗切斯特", "rochester"),
                        ("锡拉丘兹", "syracuse"),
                        ("奥尔巴尼", "albany")
                    ],
                    "得克萨斯州": [
                        ("休斯顿", "houston"),
                        ("达拉斯", "dallas"),
                        ("圣安东尼奥", "san_antonio"),
                        ("奥斯汀", "austin"),
                        ("沃思堡", "fort_worth"),
                        ("埃尔帕索", "el_paso")
                    ],
                    "佛罗里达州": [
                        ("迈阿密", "miami"),
                        ("坦帕", "tampa"),
                        ("奥兰多", "orlando"),
                        ("杰克逊维尔", "jacksonville"),
                        ("圣彼得堡", "st_petersburg")
                    ],
                    "伊利诺伊州": [
                        ("芝加哥", "chicago"),
                        ("奥罗拉", "aurora"),
                        ("罗克福德", "rockford"),
                        ("皮奥里亚", "peoria"),
                        ("内珀维尔", "naperville")
                    ],
                    "宾夕法尼亚州": [
                        ("费城", "philadelphia"),
                        ("匹兹堡", "pittsburgh"),
                        ("阿伦敦", "allentown"),
                        ("伊利", "erie"),
                        ("雷丁", "reading")
                    ],
                    "俄亥俄州": [
                        ("哥伦布", "columbus"),
                        ("克利夫兰", "cleveland"),
                        ("辛辛那提", "cincinnati"),
                        ("托莱多", "toledo"),
                        ("阿克伦", "akron")
                    ],
                    "乔治亚州": [
                        ("亚特兰大", "atlanta"),
                        ("奥古斯塔", "augusta"),
                        ("哥伦布", "columbus_ga"),
                        ("萨凡纳", "savannah"),
                        ("雅典", "athens")
                    ]
                }

                # 准备测试任务
                test_tasks = []
                for state_cn, cities in test_cities.items():
                    for city_cn, city_en in cities:
                        test_tasks.append((state_cn, city_cn, city_en))

                total_tasks = len(test_tasks)
                self.test_progress_var.set(f"🧪 开始测试 {total_tasks} 个城市...")

                # 并发测试
                with ThreadPoolExecutor(max_workers=3) as executor:
                    future_to_city = {
                        executor.submit(self.test_city_ip, state_cn, city_cn, city_en): (state_cn, city_cn, city_en)
                        for state_cn, city_cn, city_en in test_tasks
                    }

                    completed = 0
                    for future in as_completed(future_to_city):
                        result = future.result()
                        self.test_results.append(result)

                        completed += 1
                        self.test_progress_var.set(f"🧪 测试进度: {completed}/{total_tasks}")

                        if result['success'] and result['city_match']:
                            successful_cities.append(result)
                        else:
                            failed_cities.append(result)

                        time.sleep(1)  # 避免请求过快

                # 测试完成
                self.test_progress_var.set(f"✅ 测试完成: {len(successful_cities)}/{total_tasks} 成功")

                # 显示结果
                self.show_test_results(successful_cities, failed_cities)

                # 如果有成功的城市，启用更新按钮
                if successful_cities:
                    self.update_cities_btn.config(state=tk.NORMAL)

            except Exception as e:
                self.test_progress_var.set(f"❌ 测试失败: {str(e)}")
                self.log_message(f"❌ 城市测试失败: {str(e)}")
            finally:
                self.test_in_progress = False
                self.test_cities_btn.config(state=tk.NORMAL)

        Thread(target=run_test, daemon=True).start()

    def test_city_ip(self, state_cn, city_cn, city_en, timeout=30):
        """测试单个城市的IP精确性"""
        try:
            # 生成代理参数
            session_id = f"sess{random.randint(10000, 99999)}"
            session_time = 5  # 5分钟测试
            city_param = f"cc-US-city-{city_en}"

            proxy_username = f"customer-{self.config['username']}-{city_param}-sessid-{session_id}-sesstime-{session_time}"

            # 代理配置
            proxies = {
                'http': f"http://{proxy_username}:{self.config['password']}@{self.config['proxy_host']}:{self.config['proxy_port']}",
                'https': f"http://{proxy_username}:{self.config['password']}@{self.config['proxy_host']}:{self.config['proxy_port']}"
            }

            self.log_message(f"测试: {city_cn}, {state_cn} ({city_en})")

            # 获取IP信息
            response = requests.get('https://httpbin.org/ip',
                                  proxies=proxies,
                                  timeout=timeout)

            if response.status_code == 200:
                ip_data = response.json()
                ip_address = ip_data.get('origin', '').split(',')[0].strip()

                # 获取IP地理位置信息
                geo_response = requests.get(f'http://ip-api.com/json/{ip_address}',
                                          timeout=timeout)

                if geo_response.status_code == 200:
                    geo_data = geo_response.json()

                    if geo_data.get('status') == 'success':
                        actual_city = geo_data.get('city', '').lower()
                        actual_region = geo_data.get('regionName', '')
                        actual_country = geo_data.get('country', '')

                        # 检查城市匹配度
                        city_match = False

                        # 精确匹配
                        if city_en.lower().replace('_', ' ') in actual_city:
                            city_match = True
                        # 部分匹配（处理复合城市名）
                        elif any(part in actual_city for part in city_en.lower().split('_') if len(part) > 3):
                            city_match = True

                        result = {
                            'state_cn': state_cn,
                            'city_cn': city_cn,
                            'city_en': city_en,
                            'ip_address': ip_address,
                            'actual_city': geo_data.get('city', ''),
                            'actual_region': actual_region,
                            'actual_country': actual_country,
                            'city_match': city_match,
                            'success': True,
                            'error': None
                        }

                        status = "✅" if city_match else "❌"
                        self.log_message(f"  {status} IP: {ip_address} -> {geo_data.get('city', '')}, {actual_region}")

                        return result
                    else:
                        error_msg = f"地理位置查询失败: {geo_data.get('message', '未知错误')}"
                else:
                    error_msg = f"地理位置API请求失败: {geo_response.status_code}"
            else:
                error_msg = f"代理请求失败: {response.status_code}"

        except requests.exceptions.Timeout:
            error_msg = "请求超时"
        except requests.exceptions.ProxyError:
            error_msg = "代理连接失败"
        except requests.exceptions.ConnectionError:
            error_msg = "网络连接失败"
        except Exception as e:
            error_msg = f"未知错误: {str(e)}"

        self.log_message(f"  ❌ 失败: {error_msg}")

        return {
            'state_cn': state_cn,
            'city_cn': city_cn,
            'city_en': city_en,
            'success': False,
            'error': error_msg,
            'city_match': False
        }

    def show_test_results(self, successful_cities, failed_cities):
        """显示测试结果"""
        result_window = tk.Toplevel(self.root)
        result_window.title("🧪 城市测试结果")
        result_window.geometry("800x600")
        result_window.transient(self.root)

        # 居中显示
        result_window.update_idletasks()
        x = (result_window.winfo_screenwidth() // 2) - (result_window.winfo_width() // 2)
        y = (result_window.winfo_screenheight() // 2) - (result_window.winfo_height() // 2)
        result_window.geometry(f"800x600+{x}+{y}")

        # 主框架
        main_frame = ttk.Frame(result_window, padding="20")
        main_frame.pack(fill=tk.BOTH, expand=True)

        # 统计信息
        stats_frame = ttk.LabelFrame(main_frame, text="📊 测试统计", padding="10")
        stats_frame.pack(fill=tk.X, pady=(0, 15))

        total_tests = len(successful_cities) + len(failed_cities)
        success_rate = (len(successful_cities) / total_tests * 100) if total_tests > 0 else 0

        stats_text = f"总测试: {total_tests} | ✅ 成功: {len(successful_cities)} | ❌ 失败: {len(failed_cities)} | 成功率: {success_rate:.1f}%"
        ttk.Label(stats_frame, text=stats_text, font=("Microsoft YaHei", 10, "bold")).pack()

        # 创建标签页
        notebook = ttk.Notebook(main_frame)
        notebook.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        # 成功城市标签页
        success_frame = ttk.Frame(notebook)
        notebook.add(success_frame, text=f"✅ 成功城市 ({len(successful_cities)})")

        success_text = scrolledtext.ScrolledText(success_frame, height=15, font=("Consolas", 9))
        success_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for city in successful_cities:
            success_text.insert(tk.END, f"✅ {city['city_cn']}, {city['state_cn']} -> {city['actual_city']}\n")

        # 失败城市标签页
        failed_frame = ttk.Frame(notebook)
        notebook.add(failed_frame, text=f"❌ 失败城市 ({len(failed_cities)})")

        failed_text = scrolledtext.ScrolledText(failed_frame, height=15, font=("Consolas", 9))
        failed_text.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)

        for city in failed_cities:
            if city['success']:
                failed_text.insert(tk.END, f"❌ {city['city_cn']}, {city['state_cn']} -> {city.get('actual_city', 'N/A')} (位置不匹配)\n")
            else:
                failed_text.insert(tk.END, f"❌ {city['city_cn']}, {city['state_cn']} -> {city['error']}\n")

        # 按钮区域
        btn_frame = ttk.Frame(main_frame)
        btn_frame.pack(fill=tk.X)

        ttk.Button(btn_frame, text="关闭", command=result_window.destroy,
                  style="Primary.TButton", width=12).pack(side=tk.RIGHT)

    def update_verified_cities(self):
        """更新验证通过的城市列表"""
        if not self.test_results:
            messagebox.showwarning("警告", "请先进行城市测试")
            return

        # 筛选成功的城市
        successful_cities = [city for city in self.test_results if city['success'] and city['city_match']]

        if not successful_cities:
            messagebox.showinfo("提示", "没有验证通过的城市")
            return

        result = messagebox.askyesno("确认更新",
                                   f"将使用 {len(successful_cities)} 个验证通过的城市更新城市列表。\n"
                                   "这将替换当前的城市列表。\n\n"
                                   "是否继续？")
        if not result:
            return

        try:
            # 按州分组
            verified_cities = {}
            for city in successful_cities:
                state_cn = city['state_cn']
                if state_cn not in verified_cities:
                    verified_cities[state_cn] = []
                verified_cities[state_cn].append((city['city_cn'], city['city_en']))

            # 生成Python代码
            code_lines = ["# 验证通过的美国城市列表（IP精确匹配，自动生成）", "VERIFIED_US_CITIES = {"]

            for state_cn, cities in verified_cities.items():
                code_lines.append(f'    "{state_cn}": [')
                for city_cn, city_en in cities:
                    code_lines.append(f'        ("{city_cn}", "{city_en}"),')
                code_lines.append('    ],')

            code_lines.append("}")

            # 保存到文件
            with open('verified_cities.py', 'w', encoding='utf-8') as f:
                f.write('\n'.join(code_lines))

            # 更新当前城市列表
            self.us_cities = verified_cities
            self.update_city_list()

            # 禁用更新按钮
            self.update_cities_btn.config(state=tk.DISABLED)

            self.log_message(f"✅ 城市列表已更新，共 {len(successful_cities)} 个验证通过的城市")
            messagebox.showinfo("成功", f"城市列表已更新！\n共 {len(successful_cities)} 个验证通过的城市")

        except Exception as e:
            error_msg = f"更新城市列表失败: {str(e)}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

def main():
    """主函数"""
    root = tk.Tk()
    app = BitBrowserUSCityTool(root)

    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")

    root.mainloop()

if __name__ == "__main__":
    main()
