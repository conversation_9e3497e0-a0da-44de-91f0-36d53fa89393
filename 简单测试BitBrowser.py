#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单测试BitBrowser API

基于官方示例，测试基本的创建和打开浏览器功能
"""

import requests
import json
import time

def test_simple_browser():
    """测试简单的浏览器创建和打开"""
    print("=" * 50)
    print("测试BitBrowser基本功能")
    print("=" * 50)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 1. 测试创建浏览器（无代理版本）
        print("1. 创建测试浏览器...")
        create_data = {
            'name': 'test_simple',
            'remark': '简单测试',
            'proxyMethod': 2,
            'proxyType': 'noproxy',
            'host': '',
            'port': '',
            'proxyUserName': '',
            "browserFingerPrint": {
                'coreVersion': '124'
            }
        }
        
        response = requests.post(f"{url}/browser/update", 
                               data=json.dumps(create_data), 
                               headers=headers, 
                               timeout=30)
        
        print(f"创建响应状态码: {response.status_code}")
        print(f"创建响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"创建结果: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and 'data' in result:
                browser_id = result['data']['id']
                print(f"✅ 浏览器创建成功，ID: {browser_id}")
                
                # 2. 测试打开浏览器
                print("\n2. 打开浏览器...")
                open_data = {"id": browser_id}
                open_response = requests.post(f"{url}/browser/open",
                                            data=json.dumps(open_data),
                                            headers=headers,
                                            timeout=30)
                
                print(f"打开响应状态码: {open_response.status_code}")
                print(f"打开响应内容: {open_response.text}")
                
                if open_response.status_code == 200:
                    open_result = open_response.json()
                    if open_result.get('success'):
                        print("✅ 浏览器打开成功")
                        print(f"调试地址: {open_result['data'].get('http', 'N/A')}")
                        
                        # 等待5秒
                        print("\n等待5秒...")
                        time.sleep(5)
                        
                        # 3. 关闭浏览器
                        print("\n3. 关闭浏览器...")
                        close_response = requests.post(f"{url}/browser/close",
                                                     data=json.dumps(open_data),
                                                     headers=headers,
                                                     timeout=30)
                        print(f"关闭响应: {close_response.status_code}")
                        
                        # 4. 删除浏览器
                        print("\n4. 删除浏览器...")
                        delete_response = requests.post(f"{url}/browser/delete",
                                                      data=json.dumps(open_data),
                                                      headers=headers,
                                                      timeout=30)
                        print(f"删除响应: {delete_response.status_code}")
                        
                        return True
                    else:
                        print(f"❌ 浏览器打开失败: {open_result.get('msg', '未知错误')}")
                else:
                    print(f"❌ 打开请求失败: {open_response.status_code}")
            else:
                print(f"❌ 创建失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到BitBrowser，请确保BitBrowser正在运行")
    except requests.exceptions.Timeout:
        print("❌ 请求超时，BitBrowser可能响应缓慢")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def test_with_proxy():
    """测试带代理的浏览器创建"""
    print("\n" + "=" * 50)
    print("测试带Oxylabs代理的浏览器")
    print("=" * 50)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    # Oxylabs代理配置
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    city_param = "cc-US-city-los_angeles"
    session_id = "sess12345"
    session_time = 10
    
    proxy_username = f"customer-{username}-{city_param}-sessid-{session_id}-sesstime-{session_time}"
    
    try:
        print("创建带代理的浏览器...")
        create_data = {
            'name': 'test_proxy',
            'remark': '代理测试 - 洛杉矶',
            'proxyMethod': 2,
            'proxyType': 'http',
            'host': 'pr.oxylabs.io',
            'port': '7777',
            'proxyUserName': proxy_username,
            'proxyPassword': password,
            "browserFingerPrint": {
                'coreVersion': '124'
            }
        }
        
        print(f"代理用户名: {proxy_username}")
        print(f"代理配置: {create_data['host']}:{create_data['port']}")
        
        response = requests.post(f"{url}/browser/update", 
                               data=json.dumps(create_data), 
                               headers=headers, 
                               timeout=30)
        
        print(f"创建响应状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                browser_id = result['data']['id']
                print(f"✅ 代理浏览器创建成功，ID: {browser_id}")
                
                # 尝试打开
                open_data = {"id": browser_id}
                open_response = requests.post(f"{url}/browser/open",
                                            data=json.dumps(open_data),
                                            headers=headers,
                                            timeout=60)  # 代理可能需要更长时间
                
                print(f"打开响应状态码: {open_response.status_code}")
                
                if open_response.status_code == 200:
                    open_result = open_response.json()
                    if open_result.get('success'):
                        print("✅ 代理浏览器打开成功")
                        print("请在浏览器中访问 https://httpbin.org/ip 检查IP地址")
                        
                        input("按回车键继续（关闭浏览器）...")
                        
                        # 关闭和删除
                        requests.post(f"{url}/browser/close", data=json.dumps(open_data), headers=headers)
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                        print("浏览器已关闭和删除")
                        
                        return True
                    else:
                        print(f"❌ 代理浏览器打开失败: {open_result.get('msg', '未知错误')}")
                        # 删除失败的浏览器
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                else:
                    print(f"❌ 打开请求失败: {open_response.status_code}")
            else:
                print(f"❌ 代理浏览器创建失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 代理测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🚀 开始BitBrowser功能测试")
    
    # 测试基本功能
    basic_success = test_simple_browser()
    
    if basic_success:
        print("\n✅ 基本功能测试通过")
        
        # 询问是否测试代理功能
        test_proxy = input("\n是否测试Oxylabs代理功能？(y/n): ").lower().strip()
        if test_proxy == 'y':
            proxy_success = test_with_proxy()
            if proxy_success:
                print("\n✅ 代理功能测试通过")
            else:
                print("\n❌ 代理功能测试失败")
    else:
        print("\n❌ 基本功能测试失败")
        print("\n可能的解决方案:")
        print("1. 确保BitBrowser应用程序正在运行")
        print("2. 检查BitBrowser是否开启了本地API服务")
        print("3. 尝试重启BitBrowser")
        print("4. 检查防火墙设置")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
