#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国城市选择器 - 命令行版本

这是一个命令行工具，用于选择美国城市并生成Oxylabs代理参数。

功能：
- 搜索美国城市
- 选择城市
- 生成Oxylabs代理参数格式
- 保存到文件

使用方法：
1. 运行脚本
2. 输入搜索关键词
3. 选择城市编号
4. 获取代理参数

作者: AI助手
日期: 2025-01-02
"""

import re
import json
import os

class USCitySelectorCLI:
    def __init__(self):
        # 美国主要城市列表
        self.us_cities = {
            "Alabama": ["Birmingham", "Montgomery", "Mobile", "Huntsville", "Tuscaloosa"],
            "Alaska": ["Anchorage", "Fairbanks", "Juneau", "Sitka", "Ketchikan"],
            "Arizona": ["Phoenix", "Tucson", "Mesa", "Chandler", "Scottsdale", "Glendale", "Tempe"],
            "Arkansas": ["Little Rock", "Fort Smith", "Fayetteville", "Springdale", "Jonesboro"],
            "California": ["Los Angeles", "San Francisco", "San Diego", "San Jose", "Sacramento", 
                          "Oakland", "Santa Ana", "Anaheim", "Riverside", "Stockton", "Bakersfield",
                          "Fremont", "San Bernardino", "Modesto", "Oxnard", "Fontana", "Moreno Valley",
                          "Huntington Beach", "Glendale", "Santa Clarita", "Garden Grove", "Oceanside"],
            "Colorado": ["Denver", "Colorado Springs", "Aurora", "Fort Collins", "Lakewood", "Thornton"],
            "Connecticut": ["Bridgeport", "New Haven", "Hartford", "Stamford", "Waterbury", "Norwalk"],
            "Delaware": ["Wilmington", "Dover", "Newark", "Middletown", "Smyrna"],
            "Florida": ["Jacksonville", "Miami", "Tampa", "Orlando", "St. Petersburg", "Hialeah",
                       "Tallahassee", "Fort Lauderdale", "Port St. Lucie", "Cape Coral", "Pembroke Pines"],
            "Georgia": ["Atlanta", "Augusta", "Columbus", "Savannah", "Athens", "Sandy Springs"],
            "Hawaii": ["Honolulu", "Pearl City", "Hilo", "Kailua", "Waipahu"],
            "Idaho": ["Boise", "Nampa", "Meridian", "Idaho Falls", "Pocatello"],
            "Illinois": ["Chicago", "Aurora", "Rockford", "Joliet", "Naperville", "Springfield",
                        "Peoria", "Elgin", "Waukegan", "Cicero"],
            "Indiana": ["Indianapolis", "Fort Wayne", "Evansville", "South Bend", "Carmel", "Fishers"],
            "Iowa": ["Des Moines", "Cedar Rapids", "Davenport", "Sioux City", "Iowa City"],
            "Kansas": ["Wichita", "Overland Park", "Kansas City", "Topeka", "Olathe"],
            "Kentucky": ["Louisville", "Lexington", "Bowling Green", "Owensboro", "Covington"],
            "Louisiana": ["New Orleans", "Baton Rouge", "Shreveport", "Lafayette", "Lake Charles"],
            "Maine": ["Portland", "Lewiston", "Bangor", "South Portland", "Auburn"],
            "Maryland": ["Baltimore", "Frederick", "Rockville", "Gaithersburg", "Bowie", "Annapolis"],
            "Massachusetts": ["Boston", "Worcester", "Springfield", "Lowell", "Cambridge", "New Bedford"],
            "Michigan": ["Detroit", "Grand Rapids", "Warren", "Sterling Heights", "Lansing", "Ann Arbor"],
            "Minnesota": ["Minneapolis", "St. Paul", "Rochester", "Duluth", "Bloomington", "Brooklyn Park"],
            "Mississippi": ["Jackson", "Gulfport", "Southaven", "Hattiesburg", "Biloxi"],
            "Missouri": ["Kansas City", "St. Louis", "Springfield", "Independence", "Columbia", "Lee's Summit"],
            "Montana": ["Billings", "Missoula", "Great Falls", "Bozeman", "Butte"],
            "Nebraska": ["Omaha", "Lincoln", "Bellevue", "Grand Island", "Kearney"],
            "Nevada": ["Las Vegas", "Henderson", "Reno", "North Las Vegas", "Sparks", "Carson City"],
            "New Hampshire": ["Manchester", "Nashua", "Concord", "Dover", "Rochester"],
            "New Jersey": ["Newark", "Jersey City", "Paterson", "Elizabeth", "Edison", "Woodbridge"],
            "New Mexico": ["Albuquerque", "Las Cruces", "Rio Rancho", "Santa Fe", "Roswell"],
            "New York": ["New York", "Buffalo", "Rochester", "Yonkers", "Syracuse", "Albany",
                        "New Rochelle", "Mount Vernon", "Schenectady", "Utica", "White Plains"],
            "North Carolina": ["Charlotte", "Raleigh", "Greensboro", "Durham", "Winston-Salem",
                              "Fayetteville", "Cary", "Wilmington", "High Point", "Concord"],
            "North Dakota": ["Fargo", "Bismarck", "Grand Forks", "Minot", "West Fargo"],
            "Ohio": ["Columbus", "Cleveland", "Cincinnati", "Toledo", "Akron", "Dayton", "Parma"],
            "Oklahoma": ["Oklahoma City", "Tulsa", "Norman", "Broken Arrow", "Lawton", "Edmond"],
            "Oregon": ["Portland", "Eugene", "Salem", "Gresham", "Hillsboro", "Bend"],
            "Pennsylvania": ["Philadelphia", "Pittsburgh", "Allentown", "Erie", "Reading", "Scranton"],
            "Rhode Island": ["Providence", "Warwick", "Cranston", "Pawtucket", "East Providence"],
            "South Carolina": ["Columbia", "Charleston", "North Charleston", "Mount Pleasant", "Rock Hill"],
            "South Dakota": ["Sioux Falls", "Rapid City", "Aberdeen", "Brookings", "Watertown"],
            "Tennessee": ["Nashville", "Memphis", "Knoxville", "Chattanooga", "Clarksville", "Murfreesboro"],
            "Texas": ["Houston", "San Antonio", "Dallas", "Austin", "Fort Worth", "El Paso",
                     "Arlington", "Corpus Christi", "Plano", "Laredo", "Lubbock", "Garland",
                     "Irving", "Amarillo", "Grand Prairie", "Brownsville", "McKinney"],
            "Utah": ["Salt Lake City", "West Valley City", "Provo", "West Jordan", "Orem", "Sandy"],
            "Vermont": ["Burlington", "South Burlington", "Rutland", "Barre", "Montpelier"],
            "Virginia": ["Virginia Beach", "Norfolk", "Chesapeake", "Richmond", "Newport News", "Alexandria"],
            "Washington": ["Seattle", "Spokane", "Tacoma", "Vancouver", "Bellevue", "Kent"],
            "West Virginia": ["Charleston", "Huntington", "Parkersburg", "Morgantown", "Wheeling"],
            "Wisconsin": ["Milwaukee", "Madison", "Green Bay", "Kenosha", "Racine", "Appleton"],
            "Wyoming": ["Cheyenne", "Casper", "Laramie", "Gillette", "Rock Springs"]
        }
        
        # 创建所有城市的平面列表
        self.all_cities = []
        for state, cities in self.us_cities.items():
            for city in cities:
                self.all_cities.append({"city": city, "state": state, "full": f"{city}, {state}"})
    
    def search_cities(self, search_term):
        """搜索城市"""
        if not search_term:
            return self.all_cities
        
        search_term = search_term.lower()
        results = []
        for city_info in self.all_cities:
            if (search_term in city_info["city"].lower() or 
                search_term in city_info["state"].lower() or
                search_term in city_info["full"].lower()):
                results.append(city_info)
        return results
    
    def generate_proxy_parameter(self, city_name):
        """生成Oxylabs代理参数"""
        # 转换为Oxylabs格式（小写，空格替换为下划线）
        city_param = city_name.lower().replace(" ", "_")
        # 移除特殊字符
        city_param = re.sub(r'[^a-z0-9_]', '', city_param)
        return f"cc-US-city-{city_param}"
    
    def display_cities(self, cities, max_display=50):
        """显示城市列表"""
        if not cities:
            print("未找到匹配的城市。")
            return False
        
        print(f"\n找到 {len(cities)} 个城市:")
        print("-" * 50)
        
        display_count = min(len(cities), max_display)
        for i, city_info in enumerate(cities[:display_count]):
            print(f"{i+1:3d}. {city_info['full']}")
        
        if len(cities) > max_display:
            print(f"... 还有 {len(cities) - max_display} 个城市未显示")
            print("请使用更具体的搜索词来缩小范围。")
        
        return True
    
    def save_results(self, results):
        """保存结果到文件"""
        filename = "oxylabs_city_parameters.txt"
        try:
            with open(filename, "w", encoding="utf-8") as f:
                f.write("Oxylabs美国城市代理参数\n")
                f.write("=" * 50 + "\n\n")
                for result in results:
                    f.write(f"城市: {result['city']}\n")
                    f.write(f"参数: {result['parameter']}\n")
                    f.write(f"示例: curl -x pr.oxylabs.io:7777 -U \"customer-USERNAME-{result['parameter']}:PASSWORD\" https://ip.oxylabs.io/location\n")
                    f.write("-" * 30 + "\n")
            print(f"\n结果已保存到文件: {filename}")
        except Exception as e:
            print(f"保存文件时出错: {e}")
    
    def run(self):
        """运行主程序"""
        print("=" * 60)
        print("美国城市选择器 - Oxylabs代理参数生成工具")
        print("=" * 60)
        print("输入 'quit' 或 'exit' 退出程序")
        print("输入 'help' 查看帮助信息")
        
        selected_results = []
        
        while True:
            print("\n" + "-" * 40)
            search_term = input("请输入搜索关键词（城市名或州名）: ").strip()
            
            if search_term.lower() in ['quit', 'exit', 'q']:
                break
            elif search_term.lower() == 'help':
                self.show_help()
                continue
            elif search_term.lower() == 'save':
                if selected_results:
                    self.save_results(selected_results)
                else:
                    print("没有选择的城市可以保存。")
                continue
            elif search_term.lower() == 'list':
                if selected_results:
                    print("\n已选择的城市:")
                    for i, result in enumerate(selected_results, 1):
                        print(f"{i}. {result['city']} -> {result['parameter']}")
                else:
                    print("还没有选择任何城市。")
                continue
            
            # 搜索城市
            results = self.search_cities(search_term)
            
            if not self.display_cities(results):
                continue
            
            # 让用户选择城市
            try:
                choice = input("\n请输入城市编号（或按回车返回搜索）: ").strip()
                if not choice:
                    continue
                
                choice_num = int(choice)
                if 1 <= choice_num <= min(len(results), 50):
                    selected_city = results[choice_num - 1]
                    proxy_param = self.generate_proxy_parameter(selected_city["city"])
                    
                    print(f"\n✅ 已选择: {selected_city['full']}")
                    print(f"🔗 代理参数: {proxy_param}")
                    print(f"📋 完整示例:")
                    print(f"   curl -x pr.oxylabs.io:7777 -U \"customer-USERNAME-{proxy_param}:PASSWORD\" https://ip.oxylabs.io/location")
                    
                    # 添加到结果列表
                    result_item = {
                        'city': selected_city['full'],
                        'parameter': proxy_param
                    }
                    
                    if result_item not in selected_results:
                        selected_results.append(result_item)
                        print("✅ 已添加到选择列表")
                    else:
                        print("ℹ️  该城市已在选择列表中")
                    
                    # 询问是否复制到剪贴板
                    try:
                        import pyperclip
                        copy_choice = input("\n是否复制代理参数到剪贴板? (y/N): ").strip().lower()
                        if copy_choice in ['y', 'yes']:
                            pyperclip.copy(proxy_param)
                            print("✅ 已复制到剪贴板")
                    except ImportError:
                        print("💡 提示: 安装 pyperclip 库可启用复制功能 (pip install pyperclip)")
                    
                else:
                    print("❌ 无效的选择编号")
                    
            except ValueError:
                print("❌ 请输入有效的数字")
            except KeyboardInterrupt:
                print("\n\n程序被用户中断")
                break
        
        # 程序结束时的总结
        if selected_results:
            print(f"\n📊 总共选择了 {len(selected_results)} 个城市:")
            for result in selected_results:
                print(f"   • {result['city']} -> {result['parameter']}")
            
            save_choice = input("\n是否保存结果到文件? (y/N): ").strip().lower()
            if save_choice in ['y', 'yes']:
                self.save_results(selected_results)
        
        print("\n👋 感谢使用美国城市选择器！")
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🔍 搜索帮助:
- 输入城市名: 如 "new york", "los angeles"
- 输入州名: 如 "california", "texas"
- 输入部分名称: 如 "san" 会匹配所有包含"san"的城市

📋 特殊命令:
- help: 显示此帮助信息
- list: 显示已选择的城市列表
- save: 保存已选择的城市到文件
- quit/exit/q: 退出程序

💡 使用技巧:
- 使用具体的搜索词来缩小结果范围
- 可以多次选择不同的城市
- 程序会自动生成正确的Oxylabs代理参数格式
        """
        print(help_text)

def main():
    """主函数"""
    selector = USCitySelectorCLI()
    try:
        selector.run()
    except KeyboardInterrupt:
        print("\n\n程序被用户中断，再见！")
    except Exception as e:
        print(f"\n程序出现错误: {e}")

if __name__ == "__main__":
    main()
