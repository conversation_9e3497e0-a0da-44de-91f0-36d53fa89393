#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量打开多个比特浏览器窗口的脚本

这是一个独立脚本，用于批量打开多个比特浏览器窗口。
不依赖任何其他文件，只需要配置浏览器ID列表即可使用。

使用方法:
1. 修改下面的 BROWSER_IDS 列表为您的实际浏览器ID
2. 确保比特浏览器API服务正在运行
3. 运行此脚本

作者: AI助手
日期: 2025-01-02
"""

import json
import time
import requests

# ==================== 配置区域 ====================
# 请修改为您的实际浏览器ID列表
BROWSER_IDS = [
    "browser_id_1",
    "browser_id_2", 
    "browser_id_3",
    # 添加更多浏览器ID...
]

# 比特浏览器API配置
BITBROWSER_API_URL = 'http://127.0.0.1:54357'
BITBROWSER_HEADERS = {'Content-Type': 'application/json'}

# 是否自动导航到Facebook收件箱
NAVIGATE_TO_FACEBOOK = True

# Facebook收件箱URL
FACEBOOK_INBOX_URL = 'https://business.facebook.com/latest/inbox/'

# 每个浏览器窗口打开之间的等待时间（秒）
WAIT_BETWEEN_OPENS = 3

# 页面加载等待时间（秒）
PAGE_LOAD_WAIT = 3
# ==================== 配置区域结束 ====================

def open_single_browser(browser_id):
    """
    打开单个比特浏览器窗口
    
    Args:
        browser_id (str): 浏览器ID
        
    Returns:
        dict: 操作结果
    """
    print(f"  正在打开浏览器: {browser_id}")
    
    if not browser_id or not browser_id.strip():
        return {
            'success': False, 
            'message': "浏览器ID为空",
            'debugger_address': None
        }
    
    json_data = {"id": browser_id.strip()}
    
    try:
        response = requests.post(
            f"{BITBROWSER_API_URL}/browser/open",
            data=json.dumps(json_data), 
            headers=BITBROWSER_HEADERS,
            timeout=30
        )
        
        if response.status_code != 200:
            return {
                'success': False,
                'message': f"API返回状态码: {response.status_code}",
                'debugger_address': None
            }
        
        result = response.json()
        
        if result.get('success'):
            debugger_address = result.get('data', {}).get('http', '')
            return {
                'success': True,
                'message': "打开成功",
                'debugger_address': debugger_address
            }
        else:
            return {
                'success': False,
                'message': result.get('message', '未知错误'),
                'debugger_address': None
            }
            
    except requests.exceptions.Timeout:
        return {
            'success': False,
            'message': "请求超时",
            'debugger_address': None
        }
    except requests.exceptions.ConnectionError:
        return {
            'success': False,
            'message': "无法连接到API服务",
            'debugger_address': None
        }
    except Exception as e:
        return {
            'success': False,
            'message': f"意外错误: {str(e)}",
            'debugger_address': None
        }

def navigate_to_facebook(debugger_address):
    """
    导航到Facebook收件箱
    
    Args:
        debugger_address (str): 浏览器调试地址
        
    Returns:
        bool: 是否成功
    """
    try:
        from DrissionPage import ChromiumPage
        
        driver = ChromiumPage(debugger_address)
        driver.get(FACEBOOK_INBOX_URL)
        time.sleep(PAGE_LOAD_WAIT)
        return True
        
    except ImportError:
        return False  # DrissionPage未安装
    except Exception:
        return False  # 导航失败

def batch_open_browsers(browser_ids):
    """
    批量打开浏览器窗口
    
    Args:
        browser_ids (list): 浏览器ID列表
        
    Returns:
        dict: 批量操作结果
    """
    print(f"开始批量打开 {len(browser_ids)} 个浏览器窗口")
    print(f"API地址: {BITBROWSER_API_URL}")
    print(f"每个窗口间等待时间: {WAIT_BETWEEN_OPENS} 秒")
    print("-" * 50)
    
    results = []
    success_count = 0
    failed_count = 0
    
    # 检查DrissionPage是否可用
    drissionpage_available = True
    try:
        from DrissionPage import ChromiumPage
    except ImportError:
        drissionpage_available = False
        print("⚠️  DrissionPage未安装，将跳过自动导航功能")
        print("-" * 50)
    
    for i, browser_id in enumerate(browser_ids):
        print(f"[{i+1}/{len(browser_ids)}] 处理浏览器: {browser_id}")
        
        # 打开浏览器窗口
        result = open_single_browser(browser_id)
        
        if result['success']:
            print(f"  ✅ 打开成功")
            success_count += 1
            
            # 尝试导航到Facebook
            if NAVIGATE_TO_FACEBOOK and drissionpage_available and result['debugger_address']:
                print(f"  🌐 正在导航到Facebook...")
                if navigate_to_facebook(result['debugger_address']):
                    print(f"  ✅ 导航成功")
                    result['navigated'] = True
                else:
                    print(f"  ❌ 导航失败")
                    result['navigated'] = False
            else:
                result['navigated'] = False
                
        else:
            print(f"  ❌ 打开失败: {result['message']}")
            failed_count += 1
            result['navigated'] = False
        
        results.append({
            'browser_id': browser_id,
            'result': result
        })
        
        # 等待一段时间再打开下一个窗口
        if i < len(browser_ids) - 1:
            print(f"  ⏳ 等待 {WAIT_BETWEEN_OPENS} 秒...")
            time.sleep(WAIT_BETWEEN_OPENS)
        
        print()  # 空行分隔
    
    return {
        'total': len(browser_ids),
        'success_count': success_count,
        'failed_count': failed_count,
        'results': results
    }

def print_summary(batch_result):
    """
    打印批量操作结果摘要
    
    Args:
        batch_result (dict): 批量操作结果
    """
    print("=" * 60)
    print("批量打开结果摘要")
    print("=" * 60)
    print(f"总数: {batch_result['total']}")
    print(f"成功: {batch_result['success_count']}")
    print(f"失败: {batch_result['failed_count']}")
    print()
    
    print("详细结果:")
    print("-" * 60)
    for item in batch_result['results']:
        browser_id = item['browser_id']
        result = item['result']
        
        if result['success']:
            nav_status = "已导航" if result.get('navigated') else "未导航"
            print(f"✅ {browser_id} - 打开成功 ({nav_status})")
        else:
            print(f"❌ {browser_id} - 打开失败: {result['message']}")

def main():
    """主函数"""
    print("=" * 60)
    print("批量打开比特浏览器窗口工具")
    print("=" * 60)
    
    # 检查浏览器ID列表是否已配置
    if not BROWSER_IDS or BROWSER_IDS == ["browser_id_1", "browser_id_2", "browser_id_3"]:
        print("⚠️  请先配置浏览器ID列表!")
        print("   请编辑此脚本，将 BROWSER_IDS 修改为您的实际浏览器ID列表")
        
        # 允许用户临时输入ID
        user_input = input("\n或者现在输入浏览器ID，用逗号分隔 (按回车退出): ").strip()
        if not user_input:
            print("退出程序")
            return
        browser_ids = [id.strip() for id in user_input.split(',') if id.strip()]
    else:
        browser_ids = [id for id in BROWSER_IDS if id and id.strip()]
    
    if not browser_ids:
        print("❌ 没有有效的浏览器ID")
        return
    
    print(f"\n准备打开的浏览器窗口:")
    for i, browser_id in enumerate(browser_ids, 1):
        print(f"  {i}. {browser_id}")
    
    # 确认是否继续
    confirm = input(f"\n确认批量打开这 {len(browser_ids)} 个浏览器窗口? (y/N): ").strip().lower()
    if confirm not in ['y', 'yes']:
        print("取消操作")
        return
    
    print()
    
    # 执行批量打开
    batch_result = batch_open_browsers(browser_ids)
    
    # 打印结果摘要
    print_summary(batch_result)
    
    print("\n程序执行完成")

if __name__ == "__main__":
    main()
