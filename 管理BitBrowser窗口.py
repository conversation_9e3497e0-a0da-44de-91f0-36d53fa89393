#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
BitBrowser窗口管理工具

查看、关闭和删除现有的浏览器窗口，解决"已超过套餐窗口数"问题
"""

import requests
import json
import time

def list_browsers():
    """列出所有浏览器窗口"""
    print("=" * 60)
    print("查看现有浏览器窗口")
    print("=" * 60)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 尝试不同的API端点获取浏览器列表
        print("尝试 /browser/list 端点...")
        list_data = {"page": 1, "pageSize": 100}
        response = requests.post(f"{url}/browser/list",
                               data=json.dumps(list_data),
                               headers=headers,
                               timeout=30)

        # 如果第一个端点失败，尝试其他可能的端点
        if response.status_code != 200 or not response.json().get('success'):
            print("尝试 GET 方法...")
            response = requests.get(f"{url}/browser/list?page=1&pageSize=100", timeout=30)
        
        print(f"请求状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"API响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success') and 'data' in result:
                browsers = result['data']['list']
                total = result['data'].get('total', result['data'].get('totalNum', 0))
                
                print(f"\n总共有 {total} 个浏览器窗口:")
                print("-" * 60)
                
                if browsers:
                    for i, browser in enumerate(browsers, 1):
                        print(f"{i:2d}. ID: {browser.get('id', 'N/A')}")
                        print(f"    名称: {browser.get('name', 'N/A')}")
                        print(f"    备注: {browser.get('remark', 'N/A')}")
                        print(f"    状态: {browser.get('status', 'N/A')}")
                        print(f"    创建时间: {browser.get('createTime', 'N/A')}")
                        print("-" * 40)
                    
                    return browsers
                else:
                    print("没有找到浏览器窗口")
                    return []
            else:
                print(f"❌ 获取列表失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            print(f"响应内容: {response.text}")
            
    except Exception as e:
        print(f"❌ 获取浏览器列表失败: {e}")
    
    return []

def close_browser(browser_id):
    """关闭指定的浏览器窗口"""
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        close_data = {"id": browser_id}
        response = requests.post(f"{url}/browser/close",
                               data=json.dumps(close_data),
                               headers=headers,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 浏览器 {browser_id} 关闭成功")
                return True
            else:
                print(f"❌ 关闭失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 关闭请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 关闭浏览器失败: {e}")
    
    return False

def delete_browser(browser_id):
    """删除指定的浏览器窗口"""
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        delete_data = {"id": browser_id}
        response = requests.post(f"{url}/browser/delete",
                               data=json.dumps(delete_data),
                               headers=headers,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                print(f"✅ 浏览器 {browser_id} 删除成功")
                return True
            else:
                print(f"❌ 删除失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 删除请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 删除浏览器失败: {e}")
    
    return False

def batch_delete_browsers(browsers):
    """批量删除浏览器窗口"""
    print("\n" + "=" * 60)
    print("批量删除浏览器窗口")
    print("=" * 60)
    
    if not browsers:
        print("没有浏览器窗口可删除")
        return
    
    print("可删除的浏览器窗口:")
    for i, browser in enumerate(browsers, 1):
        print(f"{i:2d}. {browser.get('name', 'N/A')} (ID: {browser.get('id', 'N/A')})")
    
    try:
        choice = input(f"\n请选择要删除的窗口 (1-{len(browsers)}, 'all'删除所有, 'q'退出): ").strip().lower()
        
        if choice == 'q':
            return
        elif choice == 'all':
            confirm = input(f"确认删除所有 {len(browsers)} 个浏览器窗口？(y/n): ").strip().lower()
            if confirm == 'y':
                success_count = 0
                for browser in browsers:
                    browser_id = browser.get('id')
                    if browser_id:
                        print(f"删除浏览器: {browser.get('name', 'N/A')} ({browser_id})")
                        # 先尝试关闭
                        close_browser(browser_id)
                        time.sleep(1)  # 等待1秒
                        # 然后删除
                        if delete_browser(browser_id):
                            success_count += 1
                        time.sleep(1)  # 等待1秒避免请求过快
                
                print(f"\n✅ 成功删除 {success_count}/{len(browsers)} 个浏览器窗口")
        else:
            try:
                index = int(choice) - 1
                if 0 <= index < len(browsers):
                    browser = browsers[index]
                    browser_id = browser.get('id')
                    browser_name = browser.get('name', 'N/A')
                    
                    confirm = input(f"确认删除浏览器 '{browser_name}' ({browser_id})？(y/n): ").strip().lower()
                    if confirm == 'y':
                        print(f"删除浏览器: {browser_name}")
                        close_browser(browser_id)
                        time.sleep(1)
                        delete_browser(browser_id)
                else:
                    print("无效的选择")
            except ValueError:
                print("请输入有效的数字")
                
    except KeyboardInterrupt:
        print("\n操作已取消")

def main():
    """主函数"""
    print("🔧 BitBrowser窗口管理工具")
    print("解决'已超过套餐窗口数'问题")
    
    while True:
        print("\n" + "=" * 60)
        print("选择操作:")
        print("1. 查看所有浏览器窗口")
        print("2. 删除浏览器窗口")
        print("3. 退出")
        print("=" * 60)
        
        try:
            choice = input("请选择 (1-3): ").strip()
            
            if choice == '1':
                browsers = list_browsers()
                if browsers:
                    input("\n按回车键继续...")
                    
            elif choice == '2':
                browsers = list_browsers()
                if browsers:
                    batch_delete_browsers(browsers)
                else:
                    print("没有浏览器窗口可删除")
                    
            elif choice == '3':
                print("再见！")
                break
                
            else:
                print("无效的选择，请重试")
                
        except KeyboardInterrupt:
            print("\n\n程序已退出")
            break
        except Exception as e:
            print(f"操作失败: {e}")

if __name__ == "__main__":
    main()
