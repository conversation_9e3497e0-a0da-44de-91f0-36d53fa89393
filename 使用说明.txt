比特浏览器窗口打开工具 - 使用说明
========================================

本工具包含三个独立的Python脚本，用于打开比特浏览器窗口。
所有脚本都是完全独立的，不依赖任何其他文件。

文件列表：
---------
1. 快速打开比特浏览器.py     - 快速打开单个浏览器窗口
2. 批量打开比特浏览器.py     - 批量打开多个浏览器窗口  
3. 独立打开比特浏览器.py     - 完整功能的交互式工具

前置要求：
---------
1. 确保比特浏览器API服务正在运行
   - 默认地址: http://127.0.0.1:54357
   - 如果地址不同，请修改脚本中的 BITBROWSER_API_URL

2. 安装必要的Python库：
   pip install requests
   
3. 可选安装（用于自动导航到Facebook）：
   pip install DrissionPage

使用方法：
---------

方法1: 快速打开单个浏览器窗口
-----------------------------
1. 编辑 "快速打开比特浏览器.py"
2. 修改 BROWSER_ID = "your_browser_id_here" 为您的实际浏览器ID
3. 运行脚本: python 快速打开比特浏览器.py

特点：
- 最简单的使用方式
- 适合只需要打开一个浏览器窗口的情况
- 可以自动导航到Facebook收件箱

方法2: 批量打开多个浏览器窗口
-----------------------------
1. 编辑 "批量打开比特浏览器.py"
2. 修改 BROWSER_IDS 列表，添加您的实际浏览器ID：
   BROWSER_IDS = [
       "browser_id_1",
       "browser_id_2", 
       "browser_id_3",
   ]
3. 运行脚本: python 批量打开比特浏览器.py

特点：
- 可以一次性打开多个浏览器窗口
- 支持自定义每个窗口间的等待时间
- 显示详细的操作结果
- 可以自动导航到Facebook收件箱

方法3: 交互式完整工具
--------------------
1. 直接运行: python 独立打开比特浏览器.py
2. 根据菜单提示选择功能
3. 按提示输入浏览器ID

特点：
- 交互式菜单操作
- 支持单个和批量打开
- 支持命令行参数
- 完整的错误处理和日志记录

配置选项：
---------
每个脚本都有配置区域，您可以修改以下设置：

1. BITBROWSER_API_URL - 比特浏览器API地址
2. NAVIGATE_TO_FACEBOOK - 是否自动导航到Facebook
3. FACEBOOK_INBOX_URL - Facebook收件箱URL
4. WAIT_BETWEEN_OPENS - 批量打开时的等待时间

示例配置：
# 比特浏览器API配置
BITBROWSER_API_URL = 'http://127.0.0.1:54357'

# 是否自动导航到Facebook收件箱
NAVIGATE_TO_FACEBOOK = True

# Facebook收件箱URL
FACEBOOK_INBOX_URL = 'https://business.facebook.com/latest/inbox/'

命令行使用：
-----------
部分脚本支持命令行参数：

# 使用交互式工具直接打开指定浏览器
python 独立打开比特浏览器.py your_browser_id

错误处理：
---------
所有脚本都包含完善的错误处理：

1. API连接错误 - 检查比特浏览器API服务是否运行
2. 浏览器ID无效 - 确认浏览器ID是否正确
3. 超时错误 - 检查网络连接
4. DrissionPage未安装 - 安装后可获得完整功能

常见问题：
---------
Q: 脚本运行后没有反应？
A: 检查比特浏览器API服务是否正在运行，确认API地址是否正确。

Q: 提示"DrissionPage未安装"？
A: 这是可选功能，如需自动导航到Facebook，请运行: pip install DrissionPage

Q: 浏览器窗口打开了但没有导航到Facebook？
A: 检查网络连接，确认Facebook访问权限，或将 NAVIGATE_TO_FACEBOOK 设为 False。

Q: 批量打开时部分失败？
A: 这是正常现象，检查失败的浏览器ID是否有效，或增加等待时间。

日志文件：
---------
部分脚本会生成日志文件：
- bitbrowser_demo.log - 详细的操作日志

注意事项：
---------
1. 打开多个浏览器窗口会消耗系统资源
2. 确保网络连接稳定
3. 注意比特浏览器API的调用频率限制
4. 建议先用单个窗口测试，再进行批量操作

技术支持：
---------
如遇到问题，请检查：
1. 比特浏览器API服务状态
2. Python库安装情况
3. 浏览器ID有效性
4. 网络连接状态

更新记录：
---------
2025-01-02: 初始版本
- 创建三个独立脚本
- 支持单个和批量打开浏览器窗口
- 完整的错误处理和用户友好界面
