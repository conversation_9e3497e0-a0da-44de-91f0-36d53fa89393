#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特浏览器美国城市代理工具 - 命令行版本

这个工具结合了美国城市选择器和比特浏览器API，
可以通过命令行选择美国城市并使用对应的Oxylabs代理IP打开比特浏览器窗口。

功能：
- 命令行选择美国城市
- 配置Oxylabs代理
- 创建/更新比特浏览器窗口
- 打开浏览器窗口

使用方法：
1. 运行脚本
2. 配置Oxylabs代理账户信息
3. 选择美国城市
4. 创建并打开浏览器窗口

作者: AI助手
日期: 2025-01-02
"""

import requests
import json
import time
import re
import sys

class BitBrowserUSCityToolCLI:
    def __init__(self):
        # 比特浏览器API配置
        self.bit_api_url = "http://127.0.0.1:54345"
        self.bit_headers = {'Content-Type': 'application/json'}

        # 常见的比特浏览器端口
        self.common_ports = ["54345", "54346", "54347", "54348", "54349", "54357"]
        
        # Oxylabs代理配置
        self.oxylabs_proxy_host = "pr.oxylabs.io"
        self.oxylabs_proxy_port = "7777"
        self.oxylabs_username = "z123456_bYIud"  # 您的Oxylabs用户名
        self.oxylabs_password = "Zzr442859970~"  # 您的Oxylabs密码
        
        # 美国主要城市列表（中英文对照）
        self.us_cities = {
            "加利福尼亚州": [
                ("洛杉矶", "Los Angeles"), ("旧金山", "San Francisco"), ("圣地亚哥", "San Diego"),
                ("圣何塞", "San Jose"), ("萨克拉门托", "Sacramento")
            ],
            "纽约州": [
                ("纽约", "New York"), ("布法罗", "Buffalo"), ("罗切斯特", "Rochester"),
                ("奥尔巴尼", "Albany"), ("锡拉丘兹", "Syracuse")
            ],
            "得克萨斯州": [
                ("休斯顿", "Houston"), ("圣安东尼奥", "San Antonio"), ("达拉斯", "Dallas"),
                ("奥斯汀", "Austin"), ("沃思堡", "Fort Worth")
            ],
            "佛罗里达州": [
                ("杰克逊维尔", "Jacksonville"), ("迈阿密", "Miami"), ("坦帕", "Tampa"),
                ("奥兰多", "Orlando"), ("圣彼得堡", "St. Petersburg")
            ],
            "伊利诺伊州": [
                ("芝加哥", "Chicago"), ("奥罗拉", "Aurora"), ("罗克福德", "Rockford"),
                ("乔利埃特", "Joliet"), ("内珀维尔", "Naperville")
            ],
            "宾夕法尼亚州": [
                ("费城", "Philadelphia"), ("匹兹堡", "Pittsburgh"), ("阿伦敦", "Allentown"),
                ("伊利", "Erie"), ("雷丁", "Reading")
            ],
            "俄亥俄州": [
                ("哥伦布", "Columbus"), ("克利夫兰", "Cleveland"), ("辛辛那提", "Cincinnati"),
                ("托莱多", "Toledo"), ("阿克伦", "Akron")
            ],
            "佐治亚州": [
                ("亚特兰大", "Atlanta"), ("奥古斯塔", "Augusta"), ("哥伦布", "Columbus"),
                ("萨凡纳", "Savannah"), ("雅典", "Athens")
            ],
            "北卡罗来纳州": [
                ("夏洛特", "Charlotte"), ("罗利", "Raleigh"), ("格林斯博罗", "Greensboro"),
                ("达勒姆", "Durham"), ("温斯顿-塞勒姆", "Winston-Salem")
            ],
            "密歇根州": [
                ("底特律", "Detroit"), ("大急流城", "Grand Rapids"), ("沃伦", "Warren"),
                ("斯特林海茨", "Sterling Heights"), ("兰辛", "Lansing")
            ],
            "新泽西州": [
                ("纽瓦克", "Newark"), ("泽西城", "Jersey City"), ("帕特森", "Paterson"),
                ("伊丽莎白", "Elizabeth"), ("爱迪生", "Edison")
            ],
            "弗吉尼亚州": [
                ("弗吉尼亚海滩", "Virginia Beach"), ("诺福克", "Norfolk"), ("切萨皮克", "Chesapeake"),
                ("里士满", "Richmond"), ("纽波特纽斯", "Newport News")
            ],
            "华盛顿州": [
                ("西雅图", "Seattle"), ("斯波坎", "Spokane"), ("塔科马", "Tacoma"),
                ("温哥华", "Vancouver"), ("贝尔维尤", "Bellevue")
            ],
            "亚利桑那州": [
                ("凤凰城", "Phoenix"), ("图森", "Tucson"), ("梅萨", "Mesa"),
                ("钱德勒", "Chandler"), ("斯科茨代尔", "Scottsdale")
            ],
            "马萨诸塞州": [
                ("波士顿", "Boston"), ("伍斯特", "Worcester"), ("斯普林菲尔德", "Springfield"),
                ("洛厄尔", "Lowell"), ("剑桥", "Cambridge")
            ],
            "田纳西州": [
                ("纳什维尔", "Nashville"), ("孟菲斯", "Memphis"), ("诺克斯维尔", "Knoxville"),
                ("查塔努加", "Chattanooga"), ("克拉克斯维尔", "Clarksville")
            ],
            "印第安纳州": [
                ("印第安纳波利斯", "Indianapolis"), ("韦恩堡", "Fort Wayne"), ("埃文斯维尔", "Evansville"),
                ("南本德", "South Bend"), ("卡梅尔", "Carmel")
            ],
            "密苏里州": [
                ("堪萨斯城", "Kansas City"), ("圣路易斯", "St. Louis"), ("斯普林菲尔德", "Springfield"),
                ("独立城", "Independence"), ("哥伦比亚", "Columbia")
            ],
            "马里兰州": [
                ("巴尔的摩", "Baltimore"), ("弗雷德里克", "Frederick"), ("罗克维尔", "Rockville"),
                ("盖瑟斯堡", "Gaithersburg"), ("鲍伊", "Bowie")
            ],
            "威斯康星州": [
                ("密尔沃基", "Milwaukee"), ("麦迪逊", "Madison"), ("绿湾", "Green Bay"),
                ("基诺沙", "Kenosha"), ("拉辛", "Racine")
            ],
            "科罗拉多州": [
                ("丹佛", "Denver"), ("科罗拉多斯普林斯", "Colorado Springs"), ("奥罗拉", "Aurora"),
                ("柯林斯堡", "Fort Collins"), ("莱克伍德", "Lakewood")
            ],
            "明尼苏达州": [
                ("明尼阿波利斯", "Minneapolis"), ("圣保罗", "St. Paul"), ("罗切斯特", "Rochester"),
                ("德卢斯", "Duluth"), ("布卢明顿", "Bloomington")
            ],
            "内华达州": [
                ("拉斯维加斯", "Las Vegas"), ("亨德森", "Henderson"), ("里诺", "Reno"),
                ("北拉斯维加斯", "North Las Vegas")
            ],
            "俄勒冈州": [
                ("波特兰", "Portland"), ("尤金", "Eugene"), ("塞勒姆", "Salem"),
                ("格雷沙姆", "Gresham"), ("希尔斯伯勒", "Hillsboro")
            ],
            "犹他州": [
                ("盐湖城", "Salt Lake City"), ("西谷城", "West Valley City"), ("普罗沃", "Provo"),
                ("西约旦", "West Jordan")
            ]
        }

        # 创建所有城市的平面列表（中文显示，英文用于API）
        self.all_cities = []
        self.city_mapping = {}  # 中文显示名 -> 英文API名的映射

        for state_cn, cities in self.us_cities.items():
            for city_cn, city_en in cities:
                display_name = f"{city_cn}, {state_cn}"
                city_info = {
                    "city_cn": city_cn,
                    "city_en": city_en,
                    "state": state_cn,
                    "full": display_name
                }
                self.all_cities.append(city_info)
                self.city_mapping[display_name] = city_en
    
    def print_header(self):
        """打印程序头部"""
        print("=" * 70)
        print("比特浏览器美国城市代理工具 - 命令行版本")
        print("=" * 70)
        print("功能: 选择美国城市 + Oxylabs代理 + 比特浏览器自动化")
        print("输入 'help' 查看帮助，输入 'quit' 退出程序")
        print("-" * 70)
    
    def show_help(self):
        """显示帮助信息"""
        help_text = """
🔧 配置命令:
- config: 配置Oxylabs代理账户信息
- test: 测试比特浏览器API连接

🏙️ 城市选择:
- search <关键词>: 搜索美国城市
- list: 显示所有可用城市
- select <编号>: 选择城市

🌐 浏览器操作:
- create: 创建/更新浏览器窗口（使用选定城市的代理）
- open <浏览器ID>: 打开浏览器窗口
- close <浏览器ID>: 关闭浏览器窗口
- status: 显示当前状态

📋 其他命令:
- help: 显示此帮助信息
- clear: 清屏
- quit/exit: 退出程序
        """
        print(help_text)
    
    def configure_proxy(self):
        """配置代理信息"""
        print("\n🔧 配置Oxylabs代理信息:")
        print("-" * 30)
        
        username = input("请输入Oxylabs用户名: ").strip()
        if username:
            self.oxylabs_username = username
        
        password = input("请输入Oxylabs密码: ").strip()
        if password:
            self.oxylabs_password = password
        
        host = input(f"代理主机 (当前: {self.oxylabs_proxy_host}): ").strip()
        if host:
            self.oxylabs_proxy_host = host
        
        port = input(f"代理端口 (当前: {self.oxylabs_proxy_port}): ").strip()
        if port:
            self.oxylabs_proxy_port = port
        
        print("✅ 代理配置已更新")
    
    def detect_bit_browser_port(self):
        """检测比特浏览器API端口"""
        print("🔍 正在检测比特浏览器API端口...")

        for port in self.common_ports:
            try:
                test_url = f"http://127.0.0.1:{port}"
                # 使用POST方法和正确的参数测试
                test_data = {"page": 1, "pageSize": 10}
                response = requests.post(
                    f"{test_url}/browser/list",
                    json=test_data,
                    headers=self.bit_headers,
                    timeout=3
                )
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') is not False:  # 成功或者有数据
                        print(f"✅ 发现比特浏览器API端口: {port}")
                        self.bit_api_url = test_url
                        return True
            except:
                continue

        print("❌ 未找到可用的比特浏览器API端口")
        return False

    def test_bit_connection(self):
        """测试比特浏览器API连接"""
        try:
            print("🔍 测试比特浏览器API连接...")
            # 使用POST方法和正确的参数测试
            test_data = {"page": 1, "pageSize": 10}
            response = requests.post(
                f"{self.bit_api_url}/browser/list",
                json=test_data,
                headers=self.bit_headers,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                # 检查响应是否有效
                if 'data' in result or result.get('success') is not False:
                    browser_count = len(result.get('data', [])) if 'data' in result else 0
                    print(f"✅ 比特浏览器API连接成功，找到 {browser_count} 个浏览器窗口")
                    return True
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ API响应错误: {error_msg}")
                    return False
            else:
                print(f"❌ API连接失败: HTTP {response.status_code}")

                # 如果失败，尝试自动检测端口
                print("🔍 尝试自动检测端口...")
                return self.detect_bit_browser_port()
        except Exception as e:
            print(f"❌ API连接失败: {e}")

            # 如果失败，尝试自动检测端口
            print("🔍 尝试自动检测端口...")
            return self.detect_bit_browser_port()
    
    def search_cities(self, search_term):
        """搜索城市"""
        if not search_term:
            return self.all_cities
        
        search_term = search_term.lower()
        results = []
        for city_info in self.all_cities:
            if (search_term in city_info["city"].lower() or 
                search_term in city_info["state"].lower() or
                search_term in city_info["full"].lower()):
                results.append(city_info)
        return results
    
    def display_cities(self, cities, max_display=20):
        """显示城市列表"""
        if not cities:
            print("❌ 未找到匹配的城市")
            return False
        
        print(f"\n🏙️ 找到 {len(cities)} 个城市:")
        print("-" * 50)
        
        display_count = min(len(cities), max_display)
        for i, city_info in enumerate(cities[:display_count]):
            print(f"{i+1:3d}. {city_info['full']}")
        
        if len(cities) > max_display:
            print(f"... 还有 {len(cities) - max_display} 个城市未显示")
            print("💡 使用更具体的搜索词来缩小范围")
        
        return True
    
    def generate_city_proxy_param(self, city_display_name):
        """生成城市代理参数"""
        # 从映射中获取英文城市名
        if city_display_name in self.city_mapping:
            city_en = self.city_mapping[city_display_name]
            # 转换为Oxylabs格式
            city_param = city_en.lower().replace(" ", "_")
            city_param = re.sub(r'[^a-z0-9_]', '', city_param)
            return f"cc-US-city-{city_param}"
        else:
            # 备用方法：从显示名称提取
            city_only = city_display_name.split(", ")[0]
            city_param = city_only.lower().replace(" ", "_")
            city_param = re.sub(r'[^a-z0-9_]', '', city_param)
            return f"cc-US-city-{city_param}"
    
    def create_browser_window(self, selected_city):
        """创建浏览器窗口"""
        if not self.oxylabs_username or not self.oxylabs_password:
            print("❌ 请先配置Oxylabs代理信息 (使用 'config' 命令)")
            return None
        
        try:
            # 生成代理参数
            city_param = self.generate_city_proxy_param(selected_city)
            proxy_username = f"customer-{self.oxylabs_username}-{city_param}"
            
            print(f"🔧 创建浏览器窗口...")
            print(f"📍 城市: {selected_city}")
            print(f"🌐 代理参数: {city_param}")
            print(f"👤 代理用户名: {proxy_username}")
            
            # 准备API数据
            json_data = {
                'name': f'US_City_{city_param}',
                'remark': f'US City: {selected_city}',
                'proxyMethod': 2,  # 自定义代理
                'proxyType': 'http',
                'host': self.oxylabs_proxy_host,
                'port': self.oxylabs_proxy_port,
                'proxyUserName': proxy_username,
                'proxyPassword': self.oxylabs_password,
                "browserFingerPrint": {
                    'coreVersion': '124'
                }
            }
            
            # 发送API请求
            response = requests.post(
                f"{self.bit_api_url}/browser/update",
                data=json.dumps(json_data), 
                headers=self.bit_headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    browser_id = result['data']['id']
                    print(f"✅ 浏览器窗口创建成功!")
                    print(f"🆔 浏览器ID: {browser_id}")
                    return browser_id
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 创建失败: {error_msg}")
                    return None
            else:
                print(f"❌ API请求失败: HTTP {response.status_code}")
                return None
                
        except Exception as e:
            print(f"❌ 创建浏览器窗口时出错: {e}")
            return None
    
    def open_browser_window(self, browser_id):
        """打开浏览器窗口"""
        try:
            print(f"🚀 正在打开浏览器窗口: {browser_id}")
            
            json_data = {"id": browser_id}
            response = requests.post(
                f"{self.bit_api_url}/browser/open",
                data=json.dumps(json_data), 
                headers=self.bit_headers,
                timeout=30
            )
            
            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    print(f"✅ 浏览器窗口打开成功!")
                    print(f"🔗 调试地址: {result['data'].get('http', 'N/A')}")
                    print(f"🌐 WebSocket地址: {result['data'].get('ws', 'N/A')}")
                    return True
                else:
                    error_msg = result.get('msg', '未知错误')
                    print(f"❌ 打开失败: {error_msg}")
                    return False
            else:
                print(f"❌ API请求失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 打开浏览器窗口时出错: {e}")
            return False
    
    def close_browser_window(self, browser_id):
        """关闭浏览器窗口"""
        try:
            print(f"🔒 正在关闭浏览器窗口: {browser_id}")
            
            json_data = {"id": browser_id}
            response = requests.post(
                f"{self.bit_api_url}/browser/close",
                data=json.dumps(json_data), 
                headers=self.bit_headers,
                timeout=30
            )
            
            if response.status_code == 200:
                print(f"✅ 浏览器窗口关闭成功")
                return True
            else:
                print(f"❌ 关闭失败: HTTP {response.status_code}")
                return False
                
        except Exception as e:
            print(f"❌ 关闭浏览器窗口时出错: {e}")
            return False
    
    def show_status(self):
        """显示当前状态"""
        print("\n📊 当前状态:")
        print("-" * 30)
        print(f"🌐 比特浏览器API: {self.bit_api_url}")
        print(f"🔧 Oxylabs主机: {self.oxylabs_proxy_host}:{self.oxylabs_proxy_port}")
        print(f"👤 Oxylabs用户名: {'已配置' if self.oxylabs_username else '未配置'}")
        print(f"🔑 Oxylabs密码: {'已配置' if self.oxylabs_password else '未配置'}")
    
    def run(self):
        """运行主程序"""
        self.print_header()
        
        selected_city = None
        current_cities = []
        
        while True:
            try:
                command = input("\n> ").strip().lower()
                
                if command in ['quit', 'exit', 'q']:
                    print("👋 再见!")
                    break
                elif command == 'help':
                    self.show_help()
                elif command == 'clear':
                    import os
                    os.system('cls' if os.name == 'nt' else 'clear')
                    self.print_header()
                elif command == 'config':
                    self.configure_proxy()
                elif command == 'test':
                    self.test_bit_connection()
                elif command == 'status':
                    self.show_status()
                elif command == 'list':
                    current_cities = self.all_cities
                    self.display_cities(current_cities)
                elif command.startswith('search '):
                    search_term = command[7:].strip()
                    current_cities = self.search_cities(search_term)
                    self.display_cities(current_cities)
                elif command.startswith('select '):
                    try:
                        index = int(command[7:].strip()) - 1
                        if 0 <= index < len(current_cities):
                            selected_city = current_cities[index]['full']
                            print(f"✅ 已选择城市: {selected_city}")
                        else:
                            print("❌ 无效的城市编号")
                    except ValueError:
                        print("❌ 请输入有效的数字")
                elif command == 'create':
                    if selected_city:
                        browser_id = self.create_browser_window(selected_city)
                        if browser_id:
                            # 询问是否立即打开
                            open_now = input("是否立即打开浏览器窗口? (y/N): ").strip().lower()
                            if open_now in ['y', 'yes']:
                                self.open_browser_window(browser_id)
                    else:
                        print("❌ 请先选择一个城市")
                elif command.startswith('open '):
                    browser_id = command[5:].strip()
                    if browser_id:
                        self.open_browser_window(browser_id)
                    else:
                        print("❌ 请提供浏览器ID")
                elif command.startswith('close '):
                    browser_id = command[6:].strip()
                    if browser_id:
                        self.close_browser_window(browser_id)
                    else:
                        print("❌ 请提供浏览器ID")
                elif command == '':
                    continue
                else:
                    print("❌ 未知命令，输入 'help' 查看帮助")
                    
            except KeyboardInterrupt:
                print("\n\n👋 程序被用户中断，再见!")
                break
            except Exception as e:
                print(f"❌ 程序出现错误: {e}")

def main():
    """主函数"""
    tool = BitBrowserUSCityToolCLI()
    tool.run()

if __name__ == "__main__":
    main()
