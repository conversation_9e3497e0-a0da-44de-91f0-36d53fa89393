#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特浏览器API路径测试工具

测试不同的API路径，找到正确的比特浏览器API端点。

作者: AI助手
日期: 2025-01-02
"""

import requests
import json

def test_api_paths():
    """测试不同的API路径"""
    base_url = "http://127.0.0.1:54345"
    
    # 可能的API路径
    test_paths = [
        "/browser/list",           # 标准路径
        "/api/browser/list",       # 带api前缀
        "/v1/browser/list",        # 版本前缀
        "/browser",                # 简化路径
        "/browsers",               # 复数形式
        "/list",                   # 最简路径
        "/browser/get",            # 替代方法
        "/browser/query",          # 查询方法
        "/window/list",            # 窗口相关
        "/profile/list",           # 配置文件相关
    ]
    
    print("=" * 60)
    print("比特浏览器API路径测试")
    print("=" * 60)
    print(f"测试基础URL: {base_url}")
    print("-" * 60)
    
    working_paths = []
    
    for path in test_paths:
        full_url = f"{base_url}{path}"
        print(f"测试: {path:<20}", end=" -> ")
        
        try:
            response = requests.get(full_url, timeout=5)
            
            if response.status_code == 200:
                print("✅ 成功 (HTTP 200)")
                try:
                    data = response.json()
                    print(f"    响应类型: {type(data)}")
                    if isinstance(data, dict):
                        if 'data' in data:
                            print(f"    数据字段: {type(data['data'])}")
                        if 'success' in data:
                            print(f"    成功标志: {data['success']}")
                    working_paths.append((path, response.text[:100]))
                except:
                    print(f"    响应内容: {response.text[:50]}...")
                    working_paths.append((path, response.text[:100]))
            elif response.status_code == 404:
                print("❌ 404 Not Found")
            elif response.status_code == 405:
                print("⚠️  405 Method Not Allowed (可能需要POST)")
            else:
                print(f"⚠️  HTTP {response.status_code}")
                
        except requests.exceptions.Timeout:
            print("❌ 超时")
        except requests.exceptions.ConnectionError:
            print("❌ 连接错误")
        except Exception as e:
            print(f"❌ 错误: {e}")
    
    print("\n" + "=" * 60)
    print("测试结果")
    print("=" * 60)
    
    if working_paths:
        print("✅ 找到可用的API路径:")
        for i, (path, response) in enumerate(working_paths, 1):
            print(f"{i}. {path}")
            print(f"   响应示例: {response}")
            print()
    else:
        print("❌ 未找到任何可用的API路径")
        print("\n可能的原因:")
        print("1. 比特浏览器版本不同，使用了不同的API结构")
        print("2. API服务未启用或配置不正确")
        print("3. 需要认证或特殊头部信息")
        print("4. 使用了不同的HTTP方法（POST而不是GET）")

def test_post_methods():
    """测试POST方法"""
    base_url = "http://127.0.0.1:54345"
    
    print("\n" + "=" * 60)
    print("测试POST方法")
    print("=" * 60)
    
    # 测试POST方法的路径
    post_paths = [
        "/browser/list",
        "/api/browser/list",
        "/browser/query",
        "/list"
    ]
    
    headers = {'Content-Type': 'application/json'}
    
    for path in post_paths:
        full_url = f"{base_url}{path}"
        print(f"POST: {path:<20}", end=" -> ")
        
        try:
            # 尝试空的POST请求
            response = requests.post(full_url, json={}, headers=headers, timeout=5)
            
            if response.status_code == 200:
                print("✅ 成功 (HTTP 200)")
                try:
                    data = response.json()
                    print(f"    响应: {json.dumps(data, indent=2, ensure_ascii=False)[:200]}...")
                except:
                    print(f"    响应: {response.text[:100]}...")
            else:
                print(f"⚠️  HTTP {response.status_code}")
                
        except Exception as e:
            print(f"❌ 错误: {e}")

def main():
    """主函数"""
    try:
        test_api_paths()
        test_post_methods()
        
        print("\n" + "=" * 60)
        print("建议")
        print("=" * 60)
        print("1. 如果找到可用路径，请更新您的工具配置")
        print("2. 如果没有找到，请检查比特浏览器文档或联系技术支持")
        print("3. 确保比特浏览器客户端版本与API兼容")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
