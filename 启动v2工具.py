#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
启动比特浏览器美国城市代理工具 v2.0
"""

import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("🚀 正在启动比特浏览器美国城市代理工具 v2.0...")
    
    # 导入并运行主程序
    from 比特浏览器美国城市代理工具_v2 import main
    
    print("✅ 模块加载成功，启动界面...")
    main()
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    print("请确保所有依赖文件都在当前目录中")
    input("按回车键退出...")
except Exception as e:
    print(f"❌ 启动失败: {e}")
    import traceback
    traceback.print_exc()
    input("按回车键退出...")
