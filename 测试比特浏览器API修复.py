#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试比特浏览器API修复

验证修复后的API调用是否正常工作。

作者: AI助手
日期: 2025-01-02
"""

import requests
import json
import time

def test_browser_list():
    """测试浏览器列表API"""
    print("=" * 50)
    print("测试比特浏览器API修复")
    print("=" * 50)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    print("1. 测试浏览器列表API...")
    try:
        # 使用正确的POST方法和参数
        data = {"page": 1, "pageSize": 10}
        response = requests.post(
            f"{url}/browser/list",
            json=data,
            headers=headers,
            timeout=10
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ API调用成功")
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if 'data' in result:
                browser_count = len(result['data'])
                print(f"   找到 {browser_count} 个浏览器窗口")
                return True
            else:
                print("   ⚠️  响应中没有data字段")
                return False
        else:
            print(f"   ❌ API调用失败: HTTP {response.status_code}")
            print(f"   响应: {response.text}")
            return False
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False

def test_browser_create():
    """测试创建浏览器窗口"""
    print("\n2. 测试创建浏览器窗口...")
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    # 创建测试浏览器窗口的数据
    browser_data = {
        'name': 'API测试窗口',
        'remark': 'API修复测试',
        'proxyMethod': 2,  # 自定义代理
        'proxyType': 'http',
        'host': 'pr.oxylabs.io',
        'port': 7777,
        'proxyUserName': 'customer-z123456_bYIud-cc-US-city-new_york',  # 使用您的账户
        'proxyPassword': 'Zzr442859970~',  # 使用您的密码
        'browserFingerPrint': {
            'coreVersion': '124'
        }
    }
    
    try:
        response = requests.post(
            f"{url}/browser/update",
            json=browser_data,
            headers=headers,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 创建请求成功")
            print(f"   响应: {json.dumps(result, indent=2, ensure_ascii=False)}")
            
            if result.get('success'):
                browser_id = result['data']['id']
                print(f"   ✅ 浏览器窗口创建成功: {browser_id}")
                return browser_id
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"   ❌ 创建失败: {error_msg}")
                return None
        else:
            print(f"   ❌ 创建请求失败: HTTP {response.status_code}")
            print(f"   响应: {response.text}")
            return None
            
    except Exception as e:
        print(f"   ❌ 创建请求异常: {e}")
        return None

def test_browser_open(browser_id):
    """测试打开浏览器窗口"""
    if not browser_id:
        print("\n3. 跳过打开测试（没有有效的浏览器ID）")
        return False
        
    print(f"\n3. 测试打开浏览器窗口: {browser_id}")
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        data = {"id": browser_id}
        response = requests.post(
            f"{url}/browser/open",
            json=data,
            headers=headers,
            timeout=60
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("   ✅ 打开请求成功")
            
            if result.get('success'):
                print("   ✅ 浏览器窗口打开成功")
                print(f"   调试地址: {result['data'].get('http', 'N/A')}")
                print(f"   WebSocket: {result['data'].get('ws', 'N/A')}")
                return True
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"   ❌ 打开失败: {error_msg}")
                return False
        else:
            print(f"   ❌ 打开请求失败: HTTP {response.status_code}")
            return False
            
    except Exception as e:
        print(f"   ❌ 打开请求异常: {e}")
        return False

def test_browser_close(browser_id):
    """测试关闭浏览器窗口"""
    if not browser_id:
        print("\n4. 跳过关闭测试（没有有效的浏览器ID）")
        return
        
    print(f"\n4. 测试关闭浏览器窗口: {browser_id}")
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        data = {"id": browser_id}
        response = requests.post(
            f"{url}/browser/close",
            json=data,
            headers=headers,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 浏览器窗口关闭成功")
        else:
            print(f"   ❌ 关闭失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 关闭请求异常: {e}")

def test_browser_delete(browser_id):
    """测试删除浏览器窗口"""
    if not browser_id:
        print("\n5. 跳过删除测试（没有有效的浏览器ID）")
        return
        
    print(f"\n5. 测试删除浏览器窗口: {browser_id}")
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        data = {"id": browser_id}
        response = requests.post(
            f"{url}/browser/delete",
            json=data,
            headers=headers,
            timeout=30
        )
        
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            print("   ✅ 浏览器窗口删除成功")
        else:
            print(f"   ❌ 删除失败: HTTP {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 删除请求异常: {e}")

def main():
    """主函数"""
    print("开始测试比特浏览器API修复...")
    
    # 测试浏览器列表
    if not test_browser_list():
        print("\n❌ 基础API测试失败，请检查比特浏览器是否正在运行")
        return
    
    # 测试创建浏览器
    browser_id = test_browser_create()
    
    if browser_id:
        # 等待一下让浏览器创建完成
        print("\n   等待浏览器创建完成...")
        time.sleep(2)
        
        # 测试打开浏览器
        opened = test_browser_open(browser_id)
        
        if opened:
            # 等待一下
            print("\n   等待5秒后关闭浏览器...")
            time.sleep(5)
            
            # 测试关闭浏览器
            test_browser_close(browser_id)
        
        # 清理：删除测试浏览器
        test_browser_delete(browser_id)
    
    print("\n" + "=" * 50)
    print("测试完成")
    print("=" * 50)
    
    if browser_id:
        print("✅ API修复成功！所有功能正常工作")
        print("现在您可以使用修复后的工具了")
    else:
        print("⚠️  部分功能可能还有问题，请检查比特浏览器配置")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
