#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新功能

验证新增的功能：
1. 会话时间设置
2. 设置保存/加载
3. 城市排序
4. 改进的错误处理

作者: AI助手
日期: 2025-01-02
"""

import json
import re
import random

def test_session_time_generation():
    """测试会话时间参数生成"""
    print("=" * 60)
    print("测试会话时间参数生成")
    print("=" * 60)
    
    username = "z123456_bYIud"
    city_param = "cc-US-city-los_angeles"
    session_times = [1, 5, 10, 15, 30]
    
    for session_time in session_times:
        session_id = f"sess{random.randint(10000, 99999)}"
        proxy_username = f"customer-{username}-{city_param}-sessid-{session_id}-sesstime-{session_time}"
        
        print(f"会话时间: {session_time}分钟")
        print(f"代理用户名: {proxy_username}")
        print(f"完整代理URL: http://{proxy_username}:Zzr442859970~@pr.oxylabs.io:7777")
        print("-" * 60)

def test_settings_save_load():
    """测试设置保存和加载"""
    print("\n" + "=" * 60)
    print("测试设置保存和加载")
    print("=" * 60)
    
    # 测试设置数据
    test_settings = {
        "username": "z123456_bYIud",
        "password": "Zzr442859970~",
        "proxy_host": "pr.oxylabs.io",
        "proxy_port": "7777",
        "bit_api_url": "http://127.0.0.1:54345",
        "session_time": 15
    }
    
    settings_file = "test_settings.json"
    
    try:
        # 保存设置
        with open(settings_file, 'w', encoding='utf-8') as f:
            json.dump(test_settings, f, indent=2, ensure_ascii=False)
        print("✅ 设置保存成功")
        
        # 加载设置
        with open(settings_file, 'r', encoding='utf-8') as f:
            loaded_settings = json.load(f)
        
        print("✅ 设置加载成功")
        print("保存的设置:")
        for key, value in loaded_settings.items():
            if key == "password":
                print(f"  {key}: {'*' * len(str(value))}")
            else:
                print(f"  {key}: {value}")
        
        # 清理测试文件
        import os
        os.remove(settings_file)
        print("✅ 测试文件已清理")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")

def test_city_sorting():
    """测试城市排序"""
    print("\n" + "=" * 60)
    print("测试城市排序（按人口大小）")
    print("=" * 60)
    
    # 美国主要城市（按人口排序）
    major_cities = [
        ("纽约", "New York", "8,336,817"),
        ("洛杉矶", "Los Angeles", "3,979,576"),
        ("芝加哥", "Chicago", "2,693,976"),
        ("休斯顿", "Houston", "2,320,268"),
        ("凤凰城", "Phoenix", "1,680,992"),
        ("费城", "Philadelphia", "1,584,064"),
        ("圣安东尼奥", "San Antonio", "1,547,253"),
        ("圣地亚哥", "San Diego", "1,423,851"),
        ("达拉斯", "Dallas", "1,343,573"),
        ("圣何塞", "San Jose", "1,021,795")
    ]
    
    print("美国前10大城市（按人口排序）:")
    print("-" * 60)
    for i, (city_cn, city_en, population) in enumerate(major_cities, 1):
        print(f"{i:2d}. {city_cn} ({city_en}) - 人口: {population}")

def test_proxy_parameter_generation():
    """测试代理参数生成"""
    print("\n" + "=" * 60)
    print("测试代理参数生成")
    print("=" * 60)
    
    # 测试城市映射
    city_mapping = {
        "洛杉矶, 加利福尼亚州": "Los Angeles",
        "纽约, 纽约州": "New York",
        "芝加哥, 伊利诺伊州": "Chicago",
        "休斯顿, 得克萨斯州": "Houston",
        "圣何塞, 加利福尼亚州": "San Jose"
    }
    
    def generate_city_proxy_param(city_display_name):
        """生成城市代理参数"""
        if city_display_name in city_mapping:
            city_en = city_mapping[city_display_name]
            city_param = city_en.lower().replace(" ", "_")
            city_param = re.sub(r'[^a-z0-9_]', '', city_param)
            return f"cc-US-city-{city_param}"
        return None
    
    print("城市代理参数生成测试:")
    print("-" * 40)
    
    for city_display, city_en in city_mapping.items():
        proxy_param = generate_city_proxy_param(city_display)
        print(f"中文显示: {city_display}")
        print(f"英文名称: {city_en}")
        print(f"代理参数: {proxy_param}")
        print("-" * 40)

def test_error_handling():
    """测试错误处理改进"""
    print("\n" + "=" * 60)
    print("测试错误处理改进")
    print("=" * 60)
    
    # 模拟常见错误消息
    error_messages = [
        "已超过套餐窗口数，请在线购买",
        "浏览器正在打开中",
        "Browser is opening",
        "连接超时",
        "API请求失败",
        "未知错误"
    ]
    
    print("错误处理策略:")
    print("-" * 40)
    
    for error_msg in error_messages:
        print(f"错误消息: {error_msg}")
        
        if "正在打开中" in error_msg or "opening" in error_msg.lower():
            print("  处理策略: 等待5秒后重试（最多3次）")
        elif "超过套餐" in error_msg:
            print("  处理策略: 提示用户购买套餐或删除现有窗口")
        elif "超时" in error_msg:
            print("  处理策略: 增加超时时间，重试连接")
        else:
            print("  处理策略: 显示错误信息，记录日志")
        
        print("-" * 40)

def main():
    """主函数"""
    try:
        print("🚀 开始测试新功能")
        
        # 测试会话时间参数生成
        test_session_time_generation()
        
        # 测试设置保存和加载
        test_settings_save_load()
        
        # 测试城市排序
        test_city_sorting()
        
        # 测试代理参数生成
        test_proxy_parameter_generation()
        
        # 测试错误处理
        test_error_handling()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        print("新功能验证成功！")
        print("\n主要改进:")
        print("1. ✅ 添加了会话时间设置（1-30分钟）")
        print("2. ✅ 添加了设置保存/加载功能")
        print("3. ✅ 城市按人口大小排序")
        print("4. ✅ 改进了错误处理和重试机制")
        print("5. ✅ 代理参数包含会话时间和会话ID")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
