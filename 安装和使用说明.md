# 美国城市选择器 - Oxylabs代理参数生成工具

## 概述

这个工具包含两个版本，用于选择美国城市并生成对应的Oxylabs代理参数格式：

1. **图形界面版本** (`美国城市选择器.py`) - 提供友好的GUI界面
2. **命令行版本** (`美国城市选择器_命令行版.py`) - 适合命令行操作

## 功能特性

- ✅ 包含美国50个州的主要城市（500+个城市）
- 🔍 支持城市名称和州名搜索
- 🎯 自动生成Oxylabs代理参数格式
- 📋 一键复制到剪贴板
- 💾 保存选择结果到文件
- 🖥️ 图形界面和命令行两种操作方式

## 安装要求

### 基本要求
```bash
# Python 3.6 或更高版本
python --version
```

### 依赖库安装

#### 图形界面版本
```bash
# 安装复制功能依赖（可选）
pip install pyperclip
```

#### 命令行版本
```bash
# 安装复制功能依赖（可选）
pip install pyperclip
```

**注意**: `pyperclip` 是可选依赖，如果不安装，复制功能将不可用，但其他功能正常。

## 使用方法

### 方法1: 图形界面版本

```bash
python 美国城市选择器.py
```

**操作步骤**:
1. 在搜索框中输入城市名称或州名
2. 从列表中选择您需要的城市
3. 查看生成的代理参数
4. 点击"复制"按钮复制到剪贴板

**界面功能**:
- 🔍 实时搜索过滤
- 📋 选择城市列表
- 🎯 自动生成代理参数
- 📄 使用说明和示例

### 方法2: 命令行版本

```bash
python 美国城市选择器_命令行版.py
```

**操作步骤**:
1. 输入搜索关键词（城市名或州名）
2. 从显示的列表中选择城市编号
3. 查看生成的代理参数
4. 选择是否复制到剪贴板

**特殊命令**:
- `help` - 显示帮助信息
- `list` - 显示已选择的城市列表
- `save` - 保存结果到文件
- `quit` / `exit` / `q` - 退出程序

## 生成的参数格式

工具会自动将城市名称转换为Oxylabs要求的格式：

### 转换规则
- 城市名转为小写
- 空格替换为下划线
- 移除特殊字符
- 添加 `cc-US-city-` 前缀

### 示例转换
| 城市名称 | 生成参数 |
|---------|---------|
| New York | `cc-US-city-new_york` |
| Los Angeles | `cc-US-city-los_angeles` |
| San Francisco | `cc-US-city-san_francisco` |
| Las Vegas | `cc-US-city-las_vegas` |

## 使用示例

### 完整的curl命令示例
```bash
# 使用纽约代理
curl -x pr.oxylabs.io:7777 -U "customer-USERNAME-cc-US-city-new_york:PASSWORD" https://ip.oxylabs.io/location

# 使用洛杉矶代理
curl -x pr.oxylabs.io:7777 -U "customer-USERNAME-cc-US-city-los_angeles:PASSWORD" https://ip.oxylabs.io/location
```

### Python代码示例
```python
import requests

# 代理配置
proxy_config = {
    'http': 'http://customer-USERNAME-cc-US-city-new_york:<EMAIL>:7777',
    'https': 'http://customer-USERNAME-cc-US-city-new_york:<EMAIL>:7777'
}

# 发送请求
response = requests.get('https://ip.oxylabs.io/location', proxies=proxy_config)
print(response.text)
```

## 包含的城市

工具包含美国50个州的主要城市，包括但不限于：

### 热门城市
- **加利福尼亚州**: Los Angeles, San Francisco, San Diego, San Jose, Sacramento
- **纽约州**: New York, Buffalo, Rochester, Albany, Syracuse
- **德克萨斯州**: Houston, San Antonio, Dallas, Austin, Fort Worth
- **佛罗里达州**: Jacksonville, Miami, Tampa, Orlando, St. Petersburg
- **伊利诺伊州**: Chicago, Aurora, Rockford, Joliet, Naperville

### 完整列表
工具包含500+个美国主要城市，覆盖所有50个州。

## 文件输出

### 保存格式
选择的城市会保存到 `oxylabs_city_parameters.txt` 文件中，格式如下：

```
Oxylabs美国城市代理参数
==================================================

城市: New York, New York
参数: cc-US-city-new_york
示例: curl -x pr.oxylabs.io:7777 -U "customer-USERNAME-cc-US-city-new_york:PASSWORD" https://ip.oxylabs.io/location
------------------------------
城市: Los Angeles, California
参数: cc-US-city-los_angeles
示例: curl -x pr.oxylabs.io:7777 -U "customer-USERNAME-cc-US-city-los_angeles:PASSWORD" https://ip.oxylabs.io/location
------------------------------
```

## 故障排除

### 常见问题

#### 1. 复制功能不工作
**问题**: 点击复制按钮没有反应
**解决**: 安装pyperclip库
```bash
pip install pyperclip
```

#### 2. 图形界面无法启动
**问题**: 运行图形版本时出错
**解决**: 
- 确保安装了tkinter（通常Python自带）
- 在Linux上可能需要安装: `sudo apt-get install python3-tk`

#### 3. 找不到特定城市
**问题**: 搜索不到某个城市
**解决**: 
- 尝试搜索州名
- 使用城市名的部分关键词
- 检查拼写是否正确

#### 4. 生成的参数格式不正确
**问题**: 参数中包含特殊字符
**解决**: 工具会自动处理特殊字符，如果仍有问题，请检查原始城市名称

### 系统要求
- **操作系统**: Windows, macOS, Linux
- **Python版本**: 3.6+
- **内存**: 最少50MB
- **磁盘空间**: 最少10MB

## 更新日志

### v1.0 (2025-01-02)
- ✅ 初始版本发布
- ✅ 图形界面版本
- ✅ 命令行版本
- ✅ 支持500+美国城市
- ✅ 自动生成Oxylabs代理参数
- ✅ 复制到剪贴板功能
- ✅ 保存结果到文件

## 技术支持

如果您遇到问题或有改进建议，请检查：

1. **Python版本**: 确保使用Python 3.6+
2. **依赖库**: 确保安装了必要的库
3. **权限**: 确保有文件读写权限
4. **网络**: 复制功能需要系统剪贴板支持

## 许可证

本工具仅供学习和个人使用。使用Oxylabs服务时请遵守其服务条款。

---

**提示**: 使用代理服务时，请确保您有有效的Oxylabs账户和凭据，并遵守相关的使用条款和法律法规。
