#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试城市列表脚本
验证更新后的城市列表是否正确
"""

# 验证通过的美国城市列表（IP精确匹配，按州分组）
US_CITIES = {
    "加利福尼亚州": [
        ("圣地亚哥", "san_diego"), ("圣何塞", "san_jose"), ("洛杉矶", "los_angeles"),
        ("旧金山", "san_francisco"), ("萨克拉门托", "sacramento"), ("弗雷斯诺", "fresno"),
        ("长滩", "long_beach"), ("奥克兰", "oakland")
    ],
    "纽约州": [
        ("布法罗", "buffalo"), ("罗切斯特", "rochester"), ("锡拉丘兹", "syracuse"),
        ("奥尔巴尼", "albany")
    ],
    "得克萨斯州": [
        ("休斯顿", "houston"), ("达拉斯", "dallas"), ("奥斯汀", "austin"),
        ("沃思堡", "fort_worth")
    ],
    "佛罗里达州": [
        ("迈阿密", "miami"), ("坦帕", "tampa"), ("奥兰多", "orlando"),
        ("杰克逊维尔", "jacksonville"), ("圣彼得堡", "st_petersburg")
    ],
    "伊利诺伊州": [
        ("芝加哥", "chicago"), ("奥罗拉", "aurora"), ("皮奥里亚", "peoria")
    ],
    "宾夕法尼亚州": [
        ("匹兹堡", "pittsburgh"), ("阿伦敦", "allentown"), ("伊利", "erie"),
        ("雷丁", "reading")
    ],
    "俄亥俄州": [
        ("哥伦布", "columbus"), ("克利夫兰", "cleveland"), ("托莱多", "toledo"),
        ("阿克伦", "akron")
    ],
    "乔治亚州": [
        ("亚特兰大", "atlanta"), ("奥古斯塔", "augusta"), ("雅典", "athens"),
        ("萨凡纳", "savannah")
    ]
}

def test_city_list():
    """测试城市列表"""
    print("=== 验证通过的美国城市列表 ===")
    
    total_cities = 0
    for state_cn, cities in US_CITIES.items():
        print(f"\n{state_cn} ({len(cities)}个城市):")
        for city_cn, city_en in cities:
            proxy_param = f"cc-US-city-{city_en}"
            print(f"  {city_cn} -> {proxy_param}")
            total_cities += 1
    
    print(f"\n总计: {total_cities} 个验证通过的城市")
    
    # 测试城市映射
    print("\n=== 测试城市映射 ===")
    city_mapping = {}
    all_cities = []
    
    for state_cn, cities in US_CITIES.items():
        for city_cn, city_en in cities:
            display_name = f"{city_cn}, {state_cn}"
            all_cities.append(display_name)
            city_mapping[display_name] = city_en
    
    print(f"城市映射示例:")
    for i, (display_name, api_name) in enumerate(list(city_mapping.items())[:5]):
        print(f"  '{display_name}' -> 'cc-US-city-{api_name}'")
    
    print(f"\n界面显示城市总数: {len(all_cities)}")
    return True

if __name__ == "__main__":
    test_city_list()
