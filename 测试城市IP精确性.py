#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Oxylabs代理城市IP精确性

验证每个城市参数是否能获得对应城市的真实IP地址
"""

import requests
import json
import time
import random
from concurrent.futures import ThreadPoolExecutor, as_completed

# Oxylabs配置
OXYLABS_CONFIG = {
    "host": "pr.oxylabs.io",
    "port": "7777",
    "username": "z123456_bYIud",
    "password": "Zzr442859970~"
}

# 测试的美国城市列表（按州分组）
TEST_CITIES = {
    "加利福尼亚州": [
        ("洛杉矶", "los_angeles"),
        ("圣地亚哥", "san_diego"), 
        ("圣何塞", "san_jose"),
        ("旧金山", "san_francisco"),
        ("萨克拉门托", "sacramento"),
        ("弗雷斯诺", "fresno"),
        ("长滩", "long_beach"),
        ("奥克兰", "oakland")
    ],
    "纽约州": [
        ("纽约", "new_york"),
        ("布法罗", "buffalo"),
        ("罗切斯特", "rochester"),
        ("锡拉丘兹", "syracuse"),
        ("奥尔巴尼", "albany")
    ],
    "得克萨斯州": [
        ("休斯顿", "houston"),
        ("达拉斯", "dallas"),
        ("圣安东尼奥", "san_antonio"),
        ("奥斯汀", "austin"),
        ("沃思堡", "fort_worth"),
        ("埃尔帕索", "el_paso")
    ],
    "佛罗里达州": [
        ("迈阿密", "miami"),
        ("坦帕", "tampa"),
        ("奥兰多", "orlando"),
        ("杰克逊维尔", "jacksonville"),
        ("圣彼得堡", "st_petersburg")
    ],
    "伊利诺伊州": [
        ("芝加哥", "chicago"),
        ("奥罗拉", "aurora"),
        ("罗克福德", "rockford"),
        ("皮奥里亚", "peoria"),
        ("内珀维尔", "naperville")
    ],
    "宾夕法尼亚州": [
        ("费城", "philadelphia"),
        ("匹兹堡", "pittsburgh"),
        ("阿伦敦", "allentown"),
        ("伊利", "erie"),
        ("雷丁", "reading")
    ],
    "俄亥俄州": [
        ("哥伦布", "columbus"),
        ("克利夫兰", "cleveland"),
        ("辛辛那提", "cincinnati"),
        ("托莱多", "toledo"),
        ("阿克伦", "akron")
    ],
    "乔治亚州": [
        ("亚特兰大", "atlanta"),
        ("奥古斯塔", "augusta"),
        ("哥伦布", "columbus_ga"),
        ("萨凡纳", "savannah"),
        ("雅典", "athens")
    ]
}

def test_city_ip(state_cn, city_cn, city_en, timeout=30):
    """测试单个城市的IP精确性"""
    try:
        # 生成代理参数
        session_id = f"sess{random.randint(10000, 99999)}"
        session_time = 5  # 5分钟测试
        city_param = f"cc-US-city-{city_en}"
        
        proxy_username = f"customer-{OXYLABS_CONFIG['username']}-{city_param}-sessid-{session_id}-sesstime-{session_time}"
        
        # 代理配置
        proxies = {
            'http': f"http://{proxy_username}:{OXYLABS_CONFIG['password']}@{OXYLABS_CONFIG['host']}:{OXYLABS_CONFIG['port']}",
            'https': f"http://{proxy_username}:{OXYLABS_CONFIG['password']}@{OXYLABS_CONFIG['host']}:{OXYLABS_CONFIG['port']}"
        }
        
        print(f"测试: {city_cn}, {state_cn} ({city_en})")
        
        # 获取IP信息
        response = requests.get('https://httpbin.org/ip', 
                              proxies=proxies, 
                              timeout=timeout)
        
        if response.status_code == 200:
            ip_data = response.json()
            ip_address = ip_data.get('origin', '').split(',')[0].strip()
            
            # 获取IP地理位置信息
            geo_response = requests.get(f'http://ip-api.com/json/{ip_address}', 
                                      timeout=timeout)
            
            if geo_response.status_code == 200:
                geo_data = geo_response.json()
                
                if geo_data.get('status') == 'success':
                    actual_city = geo_data.get('city', '').lower()
                    actual_region = geo_data.get('regionName', '')
                    actual_country = geo_data.get('country', '')
                    
                    # 检查城市匹配度
                    city_match = False
                    
                    # 精确匹配
                    if city_en.lower().replace('_', ' ') in actual_city:
                        city_match = True
                    # 部分匹配（处理复合城市名）
                    elif any(part in actual_city for part in city_en.lower().split('_') if len(part) > 3):
                        city_match = True
                    
                    result = {
                        'state_cn': state_cn,
                        'city_cn': city_cn,
                        'city_en': city_en,
                        'ip_address': ip_address,
                        'actual_city': geo_data.get('city', ''),
                        'actual_region': actual_region,
                        'actual_country': actual_country,
                        'city_match': city_match,
                        'success': True,
                        'error': None
                    }
                    
                    status = "✅" if city_match else "❌"
                    print(f"  {status} IP: {ip_address} -> {geo_data.get('city', '')}, {actual_region}")
                    
                    return result
                else:
                    error_msg = f"地理位置查询失败: {geo_data.get('message', '未知错误')}"
            else:
                error_msg = f"地理位置API请求失败: {geo_response.status_code}"
        else:
            error_msg = f"代理请求失败: {response.status_code}"
            
    except requests.exceptions.Timeout:
        error_msg = "请求超时"
    except requests.exceptions.ProxyError:
        error_msg = "代理连接失败"
    except requests.exceptions.ConnectionError:
        error_msg = "网络连接失败"
    except Exception as e:
        error_msg = f"未知错误: {str(e)}"
    
    print(f"  ❌ 失败: {error_msg}")
    
    return {
        'state_cn': state_cn,
        'city_cn': city_cn,
        'city_en': city_en,
        'success': False,
        'error': error_msg,
        'city_match': False
    }

def test_all_cities(max_workers=5):
    """并发测试所有城市"""
    print("🧪 开始测试Oxylabs城市IP精确性")
    print("=" * 80)
    
    all_results = []
    successful_cities = []
    failed_cities = []
    
    # 准备所有测试任务
    test_tasks = []
    for state_cn, cities in TEST_CITIES.items():
        for city_cn, city_en in cities:
            test_tasks.append((state_cn, city_cn, city_en))
    
    print(f"总共需要测试 {len(test_tasks)} 个城市")
    print("=" * 80)
    
    # 并发执行测试
    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任务
        future_to_city = {
            executor.submit(test_city_ip, state_cn, city_cn, city_en): (state_cn, city_cn, city_en)
            for state_cn, city_cn, city_en in test_tasks
        }
        
        # 收集结果
        for future in as_completed(future_to_city):
            result = future.result()
            all_results.append(result)
            
            if result['success'] and result['city_match']:
                successful_cities.append(result)
            else:
                failed_cities.append(result)
            
            # 添加延迟避免请求过快
            time.sleep(1)
    
    return all_results, successful_cities, failed_cities

def generate_verified_city_list(successful_cities):
    """生成验证通过的城市列表代码"""
    print("\n" + "=" * 80)
    print("生成验证通过的城市列表")
    print("=" * 80)
    
    # 按州分组
    verified_cities = {}
    for city in successful_cities:
        state_cn = city['state_cn']
        if state_cn not in verified_cities:
            verified_cities[state_cn] = []
        verified_cities[state_cn].append((city['city_cn'], city['city_en']))
    
    # 生成Python代码
    code_lines = ["# 验证通过的美国城市列表（IP精确匹配）", "VERIFIED_US_CITIES = {"]
    
    for state_cn, cities in verified_cities.items():
        code_lines.append(f'    "{state_cn}": [')
        for city_cn, city_en in cities:
            code_lines.append(f'        ("{city_cn}", "{city_en}"),')
        code_lines.append('    ],')
    
    code_lines.append("}")
    
    # 保存到文件
    with open('verified_cities.py', 'w', encoding='utf-8') as f:
        f.write('\n'.join(code_lines))
    
    print(f"✅ 验证通过的城市列表已保存到 verified_cities.py")
    print(f"✅ 共有 {len(successful_cities)} 个城市通过验证")
    
    return verified_cities

def main():
    """主函数"""
    print("🌍 Oxylabs城市IP精确性测试工具")
    print("验证每个城市参数是否返回对应城市的IP地址")
    
    # 执行测试
    all_results, successful_cities, failed_cities = test_all_cities(max_workers=3)
    
    # 显示结果统计
    print("\n" + "=" * 80)
    print("测试结果统计")
    print("=" * 80)
    print(f"总测试城市数: {len(all_results)}")
    print(f"✅ 成功匹配: {len(successful_cities)}")
    print(f"❌ 失败/不匹配: {len(failed_cities)}")
    print(f"成功率: {len(successful_cities)/len(all_results)*100:.1f}%")
    
    # 显示成功的城市
    if successful_cities:
        print(f"\n✅ 验证通过的城市 ({len(successful_cities)}个):")
        for city in successful_cities:
            print(f"  • {city['city_cn']}, {city['state_cn']} -> {city['actual_city']}")
    
    # 显示失败的城市
    if failed_cities:
        print(f"\n❌ 验证失败的城市 ({len(failed_cities)}个):")
        for city in failed_cities:
            if city['success']:
                print(f"  • {city['city_cn']}, {city['state_cn']} -> {city.get('actual_city', 'N/A')} (位置不匹配)")
            else:
                print(f"  • {city['city_cn']}, {city['state_cn']} -> {city['error']}")
    
    # 生成验证通过的城市列表
    if successful_cities:
        generate_verified_city_list(successful_cities)
        
        print(f"\n🎉 测试完成！")
        print(f"请使用 verified_cities.py 中的城市列表更新您的工具")
    else:
        print(f"\n😞 没有城市通过验证，请检查Oxylabs配置")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
