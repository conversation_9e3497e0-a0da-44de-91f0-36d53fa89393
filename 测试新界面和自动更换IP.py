#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新界面和自动更换IP功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_interface():
    """测试界面"""
    try:
        # 导入主程序
        from 比特浏览器美国城市代理工具 import BitBrowserUSCityTool, BitBrowserColors
        
        print("✅ 成功导入主程序模块")
        print(f"✅ 颜色配置: {BitBrowserColors.PRIMARY}")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试 - 比特浏览器美国城市代理工具")
        root.geometry("1200x800")
        
        # 创建应用实例
        app = BitBrowserUSCityTool(root)
        print("✅ 成功创建应用实例")
        
        # 检查自动更换IP相关属性
        if hasattr(app, 'auto_change_enabled'):
            print("✅ 自动更换IP功能已添加")
        else:
            print("❌ 自动更换IP功能未找到")
        
        if hasattr(app, 'auto_change_var'):
            print("✅ 自动更换IP变量已创建")
        else:
            print("❌ 自动更换IP变量未创建")
        
        if hasattr(app, 'start_auto_change'):
            print("✅ 自动更换IP方法已添加")
        else:
            print("❌ 自动更换IP方法未添加")
        
        # 检查界面组件
        if hasattr(app, 'auto_status_label'):
            print("✅ 自动更换IP状态标签已创建")
        else:
            print("❌ 自动更换IP状态标签未创建")
        
        if hasattr(app, 'countdown_label'):
            print("✅ 倒计时标签已创建")
        else:
            print("❌ 倒计时标签未创建")
        
        print("\n🎯 测试完成，启动界面...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试新界面和自动更换IP功能...")
    test_interface()
