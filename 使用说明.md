# 比特浏览器美国城市代理工具 - 使用说明

## 🚀 快速开始

### 1. 启动工具
```bash
python 比特浏览器美国城市代理工具.py
```

### 2. 解决常见问题

#### 问题1："已超过套餐窗口数"

如果遇到"已超过套餐窗口数，请在线购买"错误，请按以下步骤操作：

#### 方法一：使用工具内置功能
1. 切换到"操作"标签页
2. 点击"查看所有窗口"按钮，查看现有窗口
3. 点击"删除所有窗口"按钮，清理现有窗口
4. 确认删除后，即可创建新窗口

#### 方法二：使用独立管理工具
```bash
python 管理BitBrowser窗口.py
```

#### 问题2："内核更新失败，请重新启动客户端再打开"

**解决方案**：
- 工具已优化，不再设置浏览器内核版本和指纹参数
- 使用BitBrowser默认配置，避免内核冲突
- 如果仍然出现，请重启BitBrowser应用程序

#### 问题3："浏览器正在打开中"（持续状态）

**解决方案**：
- 工具会自动重试（最多3次，每次间隔5秒）
- 如果持续失败，可能是BitBrowser资源不足
- 尝试关闭其他浏览器窗口或重启BitBrowser

## 📋 主要功能

### 1. 城市选择和搜索
- **中文界面**: 所有城市名称显示为中文
- **搜索功能**: 在搜索框输入城市名称进行筛选
- **按人口排序**: 城市按人口大小排序（大城市优先）
- **支持的城市**: 美国50个州的主要城市

### 2. 代理配置
- **Oxylabs集成**: 自动生成Oxylabs代理参数
- **会话时间设置**: 1-30分钟可调节
- **自动参数生成**: 包含城市、会话ID和会话时间

### 3. 浏览器窗口管理
- **创建窗口**: 基于选择的城市创建浏览器窗口
- **查看窗口**: 列出所有现有浏览器窗口
- **批量删除**: 一键删除所有窗口释放套餐限制
- **单独操作**: 打开、关闭、删除指定ID的窗口

### 4. 设置管理
- **保存设置**: 保存用户名、密码、会话时间、浏览器ID等配置
- **自动加载**: 启动时自动加载上次保存的设置
- **浏览器ID保存**: 创建浏览器后自动保存ID，下次启动可直接使用
- **配置文件**: `bitbrowser_settings.json`

## 🔧 详细操作步骤

### 步骤1：配置代理信息
1. 切换到"配置"标签页
2. 确认Oxylabs用户名和密码正确
3. 调整会话时间（1-30分钟）
4. 点击"保存设置"

### 步骤2：选择目标城市
1. 切换到"操作"标签页
2. 在搜索框输入城市名称（支持中文）
3. 从列表中选择目标城市
4. 确认选择的城市显示在"已选择"标签中

### 步骤3：管理浏览器窗口
1. 如果是首次使用，点击"查看所有窗口"检查现有窗口
2. 如果有太多窗口，点击"删除所有窗口"清理
3. 点击"创建/更新浏览器"创建新窗口
4. 创建成功后，点击"打开浏览器"

### 步骤4：验证代理效果
1. 在打开的浏览器中访问 `https://httpbin.org/ip`
2. 检查返回的IP地址是否为美国对应城市的IP
3. 确认代理工作正常

## ⚙️ 代理参数格式

### Oxylabs代理配置
```
代理服务器: pr.oxylabs.io:7777
用户名格式: customer-z123456_bYIud-cc-US-city-{城市}-sessid-{会话ID}-sesstime-{分钟数}
密码: Zzr442859970~
```

### 示例代理用户名
```
customer-z123456_bYIud-cc-US-city-los_angeles-sessid-sess12345-sesstime-15
```

## 🌍 支持的美国城市

### 主要州和城市（按人口排序）

**加利福尼亚州**
- 洛杉矶 (Los Angeles)
- 圣地亚哥 (San Diego) 
- 圣何塞 (San Jose)
- 旧金山 (San Francisco)
- 萨克拉门托 (Sacramento)

**纽约州**
- 纽约 (New York)
- 布法罗 (Buffalo)
- 罗切斯特 (Rochester)
- 锡拉丘兹 (Syracuse)
- 奥尔巴尼 (Albany)

**得克萨斯州**
- 休斯顿 (Houston)
- 达拉斯 (Dallas)
- 圣安东尼奥 (San Antonio)
- 奥斯汀 (Austin)
- 沃思堡 (Fort Worth)

**佛罗里达州**
- 迈阿密 (Miami)
- 坦帕 (Tampa)
- 奥兰多 (Orlando)
- 杰克逊维尔 (Jacksonville)
- 圣彼得堡 (St. Petersburg)

*...以及其他46个州的主要城市*

## 🔍 故障排除

### 常见问题和解决方案

#### 1. "已超过套餐窗口数"
**解决方案**: 
- 使用"删除所有窗口"功能清理现有窗口
- 或者购买更多BitBrowser套餐

#### 2. "浏览器正在打开中"
**解决方案**: 
- 工具会自动重试（最多3次）
- 每次重试间隔5秒
- 如果仍然失败，请等待几分钟后重试

#### 3. 连接超时
**解决方案**: 
- 确保BitBrowser应用程序正在运行
- 检查API地址是否正确（默认：http://127.0.0.1:54345）
- 重启BitBrowser应用程序

#### 4. 代理连接失败
**解决方案**: 
- 检查Oxylabs账户余额
- 确认用户名和密码正确
- 验证代理服务器地址和端口

#### 5. 城市搜索不工作
**解决方案**: 
- 尝试输入城市的中文名称
- 清空搜索框显示所有城市
- 重启工具

## 📝 注意事项

1. **BitBrowser要求**: 确保BitBrowser应用程序正在运行且开启API服务
2. **网络连接**: 需要稳定的网络连接访问Oxylabs代理服务
3. **账户余额**: 确保Oxylabs账户有足够余额
4. **会话时间**: 虽然Oxylabs支持最高24小时，工具限制为30分钟
5. **窗口限制**: 根据BitBrowser套餐限制创建窗口数量

## 🆘 获取帮助

如果遇到问题：
1. 查看"日志"标签页的详细错误信息
2. 使用"测试连接"按钮检查BitBrowser连接
3. 运行独立的测试工具进行诊断：
   ```bash
   python 简单测试BitBrowser.py
   python 测试BitBrowser连接.py
   ```

## 📊 工具文件说明

- `比特浏览器美国城市代理工具.py` - 主工具（GUI版本）
- `管理BitBrowser窗口.py` - 独立的窗口管理工具
- `简单测试BitBrowser.py` - BitBrowser功能测试工具
- `测试简化配置.py` - 测试简化配置避免内核错误
- `测试BitBrowser连接.py` - 连接诊断工具
- `bitbrowser_settings.json` - 设置保存文件（自动生成）

## 🔄 最新改进 (v2.0)

### ✅ 已修复的问题
1. **内核更新失败** - 移除了所有浏览器指纹和内核版本设置
2. **配置过于复杂** - 简化了浏览器配置界面
3. **浏览器ID丢失** - 添加了自动保存和加载浏览器ID功能
4. **设置不持久** - 创建浏览器后自动保存所有设置

### 🆕 新增功能
1. **简化配置** - 只保留必要的窗口名称设置
2. **智能保存** - 创建浏览器后自动保存ID和设置
3. **状态恢复** - 启动时自动恢复上次的浏览器ID
4. **默认配置** - 使用BitBrowser默认配置避免兼容性问题
