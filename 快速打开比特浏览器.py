#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速打开比特浏览器窗口的简单脚本

这是一个极简的独立脚本，用于快速打开指定的比特浏览器窗口。
不依赖任何其他文件，只需要修改浏览器ID即可使用。

使用方法:
1. 修改下面的 BROWSER_ID 为您的实际浏览器ID
2. 确保比特浏览器API服务正在运行
3. 运行此脚本

作者: AI助手
日期: 2025-01-02
"""

import json
import time
import requests

# ==================== 配置区域 ====================
# 请修改为您的实际浏览器ID
BROWSER_ID = "your_browser_id_here"

# 比特浏览器API配置
BITBROWSER_API_URL = 'http://127.0.0.1:54357'
BITBROWSER_HEADERS = {'Content-Type': 'application/json'}

# 是否自动导航到Facebook收件箱
NAVIGATE_TO_FACEBOOK = True

# Facebook收件箱URL
FACEBOOK_INBOX_URL = 'https://business.facebook.com/latest/inbox/'
# ==================== 配置区域结束 ====================

def open_bitbrowser_window(browser_id):
    """
    打开指定ID的比特浏览器窗口
    
    Args:
        browser_id (str): 浏览器ID
        
    Returns:
        dict: API响应结果
    """
    print(f"正在打开比特浏览器窗口，ID: {browser_id}")
    print(f"API地址: {BITBROWSER_API_URL}")
    
    if not browser_id or not browser_id.strip():
        print("❌ 错误: 未提供有效的浏览器ID")
        return {'success': False, 'message': "未提供有效的浏览器ID"}
    
    # 准备API请求数据
    json_data = {"id": browser_id.strip()}
    
    try:
        # 发送API请求
        response = requests.post(
            f"{BITBROWSER_API_URL}/browser/open",
            data=json.dumps(json_data), 
            headers=BITBROWSER_HEADERS,
            timeout=30
        )
        
        # 检查HTTP状态码
        if response.status_code != 200:
            error_msg = f"API返回状态码: {response.status_code}"
            print(f"❌ 错误: {error_msg}")
            return {'success': False, 'message': error_msg}
        
        # 解析响应
        result = response.json()
        
        if result.get('success'):
            print(f"✅ 比特浏览器窗口打开成功!")
            print(f"   浏览器ID: {browser_id}")
            if 'data' in result and 'http' in result['data']:
                print(f"   调试地址: {result['data']['http']}")
            return result
        else:
            error_msg = result.get('message', '未知错误')
            print(f"❌ 打开失败: {error_msg}")
            return result
            
    except requests.exceptions.Timeout:
        error_msg = "请求超时 (30秒)"
        print(f"❌ 错误: {error_msg}")
        return {'success': False, 'message': error_msg}
        
    except requests.exceptions.ConnectionError:
        error_msg = f"无法连接到比特浏览器API ({BITBROWSER_API_URL})"
        print(f"❌ 错误: {error_msg}")
        print("   请确保比特浏览器API服务正在运行")
        return {'success': False, 'message': error_msg}
        
    except json.JSONDecodeError:
        error_msg = "API返回的响应不是有效的JSON格式"
        print(f"❌ 错误: {error_msg}")
        return {'success': False, 'message': error_msg}
        
    except Exception as e:
        error_msg = f"发生意外错误: {str(e)}"
        print(f"❌ 错误: {error_msg}")
        return {'success': False, 'message': error_msg}

def connect_and_navigate(debugger_address):
    """
    连接到浏览器并导航到Facebook收件箱
    
    Args:
        debugger_address (str): 浏览器调试地址
        
    Returns:
        bool: 是否成功
    """
    try:
        # 尝试导入DrissionPage
        from DrissionPage import ChromiumPage
        
        print(f"正在连接到浏览器...")
        driver = ChromiumPage(debugger_address)
        print("✅ 成功连接到浏览器")
        
        if NAVIGATE_TO_FACEBOOK:
            print(f"正在导航到Facebook收件箱...")
            driver.get(FACEBOOK_INBOX_URL)
            time.sleep(3)  # 等待页面加载
            print("✅ 成功导航到Facebook收件箱")
            
            # 显示页面信息
            try:
                print(f"   当前页面URL: {driver.url}")
                print(f"   页面标题: {driver.title}")
            except:
                pass
        
        print("🎉 浏览器已准备就绪，可以开始使用!")
        return True
        
    except ImportError:
        print("⚠️  警告: 未安装DrissionPage库")
        print("   浏览器窗口已打开，但无法自动导航")
        print("   如需自动导航功能，请运行: pip install DrissionPage")
        return False
        
    except Exception as e:
        print(f"❌ 连接浏览器时出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("快速打开比特浏览器窗口工具")
    print("=" * 60)
    
    # 检查浏览器ID是否已配置
    if BROWSER_ID == "your_browser_id_here":
        print("⚠️  请先配置浏览器ID!")
        print("   请编辑此脚本，将 BROWSER_ID 修改为您的实际浏览器ID")
        
        # 允许用户临时输入ID
        user_input = input("\n或者现在输入浏览器ID (按回车退出): ").strip()
        if not user_input:
            print("退出程序")
            return
        browser_id = user_input
    else:
        browser_id = BROWSER_ID
    
    print(f"\n使用浏览器ID: {browser_id}")
    
    # 第一步：打开浏览器窗口
    result = open_bitbrowser_window(browser_id)
    
    if not result.get('success'):
        print("\n程序结束")
        return
    
    # 第二步：连接并导航（如果有DrissionPage）
    if 'data' in result and 'http' in result['data']:
        debugger_address = result['data']['http']
        print(f"\n开始连接和导航...")
        connect_and_navigate(debugger_address)
    
    print("\n程序执行完成")

if __name__ == "__main__":
    main()
