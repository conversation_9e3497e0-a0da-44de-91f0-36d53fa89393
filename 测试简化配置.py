#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简化的BitBrowser配置

不设置任何浏览器指纹或内核版本，避免"内核更新失败"错误
"""

import requests
import json
import time

def test_simplified_browser():
    """测试简化配置的浏览器创建"""
    print("=" * 60)
    print("测试简化配置的BitBrowser创建")
    print("=" * 60)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 1. 测试无代理版本（最简配置）
        print("1. 测试最简配置（无代理）...")
        simple_data = {
            'name': 'test_simple_config',
            'remark': '简化配置测试',
            'proxyMethod': 2,
            'proxyType': 'noproxy',
            'browserFingerPrint': {}  # 空的指纹配置，让BitBrowser使用默认值
        }
        
        response = requests.post(f"{url}/browser/update", 
                               data=json.dumps(simple_data), 
                               headers=headers, 
                               timeout=30)
        
        print(f"创建响应状态码: {response.status_code}")
        print(f"创建响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                browser_id = result['data']['id']
                print(f"✅ 简化配置浏览器创建成功，ID: {browser_id}")
                
                # 尝试打开
                print("\n2. 尝试打开浏览器...")
                open_data = {"id": browser_id}
                open_response = requests.post(f"{url}/browser/open",
                                            data=json.dumps(open_data),
                                            headers=headers,
                                            timeout=30)
                
                print(f"打开响应状态码: {open_response.status_code}")
                print(f"打开响应内容: {open_response.text}")
                
                if open_response.status_code == 200:
                    open_result = open_response.json()
                    if open_result.get('success'):
                        print("✅ 简化配置浏览器打开成功！")
                        print("这证明不设置browserFingerPrint可以避免内核更新失败")
                        
                        input("按回车键继续（将关闭浏览器）...")
                        
                        # 关闭和删除
                        requests.post(f"{url}/browser/close", data=json.dumps(open_data), headers=headers)
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                        print("浏览器已关闭和删除")
                        
                        return True
                    else:
                        error_msg = open_result.get('msg', '未知错误')
                        print(f"❌ 打开失败: {error_msg}")
                        # 删除失败的浏览器
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                else:
                    print(f"❌ 打开请求失败: {open_response.status_code}")
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"❌ 创建失败: {error_msg}")
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def test_proxy_simplified():
    """测试带代理的简化配置"""
    print("\n" + "=" * 60)
    print("测试带代理的简化配置")
    print("=" * 60)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    # 简化的代理配置
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    city_param = "cc-US-city-new_york"
    session_id = "sess99999"
    session_time = 5
    
    proxy_username = f"customer-{username}-{city_param}-sessid-{session_id}-sesstime-{session_time}"
    
    try:
        print("创建带代理的简化配置浏览器...")
        proxy_data = {
            'name': 'test_proxy_simple',
            'remark': '代理简化配置测试 - 纽约',
            'proxyMethod': 2,
            'proxyType': 'http',
            'host': 'pr.oxylabs.io',
            'port': '7777',
            'proxyUserName': proxy_username,
            'proxyPassword': password,
            'browserFingerPrint': {}  # 空的指纹配置，避免内核冲突
        }
        
        print(f"代理用户名: {proxy_username}")
        
        response = requests.post(f"{url}/browser/update", 
                               data=json.dumps(proxy_data), 
                               headers=headers, 
                               timeout=30)
        
        print(f"创建响应状态码: {response.status_code}")
        print(f"创建响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success') and 'data' in result:
                browser_id = result['data']['id']
                print(f"✅ 代理简化配置浏览器创建成功，ID: {browser_id}")
                
                # 尝试打开
                print("\n尝试打开代理浏览器...")
                open_data = {"id": browser_id}
                open_response = requests.post(f"{url}/browser/open",
                                            data=json.dumps(open_data),
                                            headers=headers,
                                            timeout=60)
                
                print(f"打开响应状态码: {open_response.status_code}")
                print(f"打开响应内容: {open_response.text}")
                
                if open_response.status_code == 200:
                    open_result = open_response.json()
                    if open_result.get('success'):
                        print("✅ 代理简化配置浏览器打开成功！")
                        print("请在浏览器中访问 https://httpbin.org/ip 检查IP地址")
                        print("如果显示纽约IP，说明代理配置成功")
                        
                        input("按回车键继续（将关闭浏览器）...")
                        
                        # 关闭和删除
                        requests.post(f"{url}/browser/close", data=json.dumps(open_data), headers=headers)
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                        print("代理浏览器已关闭和删除")
                        
                        return True
                    else:
                        error_msg = open_result.get('msg', '未知错误')
                        print(f"❌ 代理浏览器打开失败: {error_msg}")
                        # 删除失败的浏览器
                        requests.post(f"{url}/browser/delete", data=json.dumps(open_data), headers=headers)
                else:
                    print(f"❌ 打开请求失败: {open_response.status_code}")
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"❌ 代理浏览器创建失败: {error_msg}")
        else:
            print(f"❌ 创建请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 代理测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🧪 测试简化的BitBrowser配置")
    print("目标：避免'内核更新失败'错误")
    
    # 测试基本简化配置
    basic_success = test_simplified_browser()
    
    if basic_success:
        print("\n✅ 基本简化配置测试通过")
        
        # 询问是否测试代理
        test_proxy = input("\n是否测试代理简化配置？(y/n): ").lower().strip()
        if test_proxy == 'y':
            proxy_success = test_proxy_simplified()
            if proxy_success:
                print("\n✅ 代理简化配置测试通过")
                print("\n🎉 所有测试通过！简化配置可以避免内核更新失败")
            else:
                print("\n❌ 代理简化配置测试失败")
    else:
        print("\n❌ 基本简化配置测试失败")
        print("\n可能的原因:")
        print("1. BitBrowser未运行或API服务未开启")
        print("2. 仍然存在套餐窗口数限制")
        print("3. 其他BitBrowser配置问题")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
