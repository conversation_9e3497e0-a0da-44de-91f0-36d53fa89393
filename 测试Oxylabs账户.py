#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试Oxylabs账户配置

验证您的Oxylabs账户和密码是否正确配置。

作者: AI助手
日期: 2025-01-02
"""

import requests
import json

def test_oxylabs_account():
    """测试Oxylabs账户配置"""
    print("=" * 60)
    print("测试Oxylabs账户配置")
    print("=" * 60)
    
    # 您的账户信息
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    proxy_host = "pr.oxylabs.io"
    proxy_port = "7777"
    
    print(f"账户: {username}")
    print(f"代理服务器: {proxy_host}:{proxy_port}")
    print("-" * 60)
    
    # 测试不同的城市参数
    test_cities = [
        "cc-US-city-new_york",
        "cc-US-city-los_angeles", 
        "cc-US-city-chicago",
        "cc-US-city-houston",
        "cc-US-city-phoenix"
    ]
    
    for i, city_param in enumerate(test_cities, 1):
        print(f"\n{i}. 测试城市参数: {city_param}")
        print("-" * 40)
        
        # 构建完整的用户名
        full_username = f"customer-{username}-{city_param}"
        print(f"完整用户名: {full_username}")
        
        # 设置代理
        proxies = {
            'http': f'http://{full_username}:{password}@{proxy_host}:{proxy_port}',
            'https': f'http://{full_username}:{password}@{proxy_host}:{proxy_port}'
        }
        
        try:
            # 测试IP位置
            print("正在测试IP位置...", end=" ")
            response = requests.get(
                'https://ip.oxylabs.io/location',
                proxies=proxies,
                timeout=15
            )
            
            if response.status_code == 200:
                location_data = response.json()
                print("✅ 成功")
                print(f"   IP: {location_data.get('ip', 'N/A')}")
                print(f"   国家: {location_data.get('country', 'N/A')}")
                print(f"   城市: {location_data.get('city', 'N/A')}")
                print(f"   地区: {location_data.get('region', 'N/A')}")
                
                # 验证是否是美国IP
                if location_data.get('country') == 'United States':
                    print("   ✅ 确认为美国IP")
                else:
                    print(f"   ⚠️  IP不在美国: {location_data.get('country')}")
                    
            else:
                print(f"❌ 失败 (HTTP {response.status_code})")
                print(f"   响应: {response.text[:100]}")
                
        except requests.exceptions.ProxyError as e:
            print("❌ 代理错误")
            print(f"   错误: {e}")
            print("   可能原因: 账户信息错误或代理服务器不可用")
            
        except requests.exceptions.Timeout:
            print("❌ 连接超时")
            print("   可能原因: 网络问题或代理服务器响应慢")
            
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def test_basic_connection():
    """测试基础连接"""
    print("\n" + "=" * 60)
    print("测试基础连接")
    print("=" * 60)
    
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    proxy_host = "pr.oxylabs.io"
    proxy_port = "7777"
    
    # 使用基础用户名（不带城市参数）
    basic_username = f"customer-{username}"
    
    print(f"基础用户名: {basic_username}")
    
    proxies = {
        'http': f'http://{basic_username}:{password}@{proxy_host}:{proxy_port}',
        'https': f'http://{basic_username}:{password}@{proxy_host}:{proxy_port}'
    }
    
    try:
        print("正在测试基础连接...", end=" ")
        response = requests.get(
            'https://ip.oxylabs.io/location',
            proxies=proxies,
            timeout=15
        )
        
        if response.status_code == 200:
            location_data = response.json()
            print("✅ 成功")
            print(f"   IP: {location_data.get('ip', 'N/A')}")
            print(f"   国家: {location_data.get('country', 'N/A')}")
            print(f"   城市: {location_data.get('city', 'N/A')}")
        else:
            print(f"❌ 失败 (HTTP {response.status_code})")
            
    except Exception as e:
        print(f"❌ 错误: {e}")

def show_curl_examples():
    """显示curl命令示例"""
    print("\n" + "=" * 60)
    print("curl命令示例")
    print("=" * 60)
    
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    
    examples = [
        ("纽约", "cc-US-city-new_york"),
        ("洛杉矶", "cc-US-city-los_angeles"),
        ("芝加哥", "cc-US-city-chicago"),
        ("休斯顿", "cc-US-city-houston")
    ]
    
    for city_name, city_param in examples:
        full_username = f"customer-{username}-{city_param}"
        curl_cmd = f"curl 'https://ip.oxylabs.io/location' -U '{full_username}:{password}' -x 'pr.oxylabs.io:7777'"
        print(f"\n{city_name}:")
        print(f"  {curl_cmd}")

def main():
    """主函数"""
    try:
        # 测试基础连接
        test_basic_connection()
        
        # 测试城市参数
        test_oxylabs_account()
        
        # 显示curl示例
        show_curl_examples()
        
        print("\n" + "=" * 60)
        print("测试完成")
        print("=" * 60)
        print("如果所有测试都成功，说明您的账户配置正确")
        print("现在可以使用修复后的比特浏览器工具了")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中出现错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
