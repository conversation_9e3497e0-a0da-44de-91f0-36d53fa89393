#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
独立的比特浏览器窗口打开脚本

这是一个完全独立的脚本，不依赖任何其他文件。
可以直接运行来打开指定的比特浏览器窗口。

使用方法:
1. 确保比特浏览器API服务正在运行 (默认地址: http://127.0.0.1:54357)
2. 修改下面的浏览器ID为您实际的浏览器ID
3. 运行此脚本

作者: AI助手
日期: 2025-01-02
"""

import json
import time
import logging
import requests

# 尝试导入DrissionPage，如果失败则提示安装
try:
    from DrissionPage import ChromiumPage
    DRISSIONPAGE_AVAILABLE = True
except ImportError:
    print("警告: 未安装DrissionPage库，请运行: pip install DrissionPage")
    DRISSIONPAGE_AVAILABLE = False

# 比特浏览器API配置
BITBROWSER_API_URL = 'http://127.0.0.1:54357'
BITBROWSER_HEADERS = {'Content-Type': 'application/json'}

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('bitbrowser_demo.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def open_bitbrowser_window(browser_id):
    """
    通过比特浏览器API打开指定ID的浏览器窗口
    
    Args:
        browser_id: 浏览器ID
        
    Returns:
        API响应字典
    """
    if not browser_id or not browser_id.strip():
        logger.error("尝试打开比特浏览器时未提供有效的浏览器ID")
        return {'success': False, 'message': "未提供有效的浏览器ID"}
    
    json_data = {"id": f'{browser_id.strip()}'}
    try:
        logger.info(f"正在打开比特浏览器窗口，ID: {browser_id}")
        response = requests.post(
            f"{BITBROWSER_API_URL}/browser/open",
            data=json.dumps(json_data), 
            headers=BITBROWSER_HEADERS,
            timeout=30  # 添加超时
        )
        
        # 检查HTTP响应状态
        if response.status_code != 200:
            logger.error(f"比特浏览器API返回非200状态码: {response.status_code}")
            return {'success': False, 'message': f"API返回状态码: {response.status_code}"}
        
        res = response.json()
        if res.get('success'):
            logger.info(f"比特浏览器窗口打开成功，ID: {browser_id}")
        else:
            logger.error(f"比特浏览器API返回错误: {res.get('message', '未知错误')}")
        return res
    except requests.exceptions.Timeout:
        error_msg = f"请求比特浏览器API超时 (30秒)"
        logger.error(error_msg)
        return {'success': False, 'message': error_msg}
    except requests.exceptions.RequestException as e:
        error_msg = f"请求比特浏览器API时出错: {str(e)}"
        logger.error(error_msg)
        return {'success': False, 'message': error_msg}
    except json.JSONDecodeError:
        error_msg = "比特浏览器API返回的响应不是有效的JSON"
        logger.error(error_msg)
        return {'success': False, 'message': error_msg}
    except Exception as e:
        error_msg = f"打开比特浏览器时发生意外错误: {str(e)}"
        logger.error(error_msg)
        return {'success': False, 'message': error_msg}

def connect_to_browser(debugger_address, navigate_to_facebook=True):
    """
    连接到已打开的比特浏览器并可选择导航到Facebook
    
    Args:
        debugger_address: 浏览器调试地址
        navigate_to_facebook: 是否导航到Facebook收件箱
        
    Returns:
        DrissionPage驱动实例或None
    """
    if not DRISSIONPAGE_AVAILABLE:
        print("错误: DrissionPage库未安装，无法连接到浏览器")
        return None
    
    try:
        logger.info(f"正在连接到浏览器调试地址: {debugger_address}")
        driver = ChromiumPage(debugger_address)
        logger.info("DrissionPage连接成功")
        
        if navigate_to_facebook:
            target_url = 'https://business.facebook.com/latest/inbox/'
            logger.info(f"正在导航到: {target_url}")
            driver.get(target_url)
            time.sleep(3)  # 等待页面加载
            logger.info("页面导航完成")
        
        return driver
    except Exception as e:
        logger.error(f"连接到浏览器时出错: {e}")
        return None

def open_and_connect_browser(browser_id, navigate_to_facebook=True):
    """
    打开比特浏览器窗口并连接
    
    Args:
        browser_id: 浏览器ID
        navigate_to_facebook: 是否导航到Facebook收件箱
        
    Returns:
        tuple: (success, driver, message)
    """
    print(f"正在打开比特浏览器窗口: {browser_id}")
    print(f"比特浏览器API地址: {BITBROWSER_API_URL}")
    
    # 第一步：打开浏览器窗口
    api_result = open_bitbrowser_window(browser_id)
    
    if not api_result.get('success'):
        error_msg = f"打开浏览器失败: {api_result.get('message', '未知错误')}"
        print(f"❌ {error_msg}")
        return False, None, error_msg
    
    print("✅ 比特浏览器窗口打开成功")
    
    # 第二步：连接到浏览器
    debugger_address = api_result['data']['http']
    print(f"调试地址: {debugger_address}")
    
    driver = connect_to_browser(debugger_address, navigate_to_facebook)
    
    if driver:
        success_msg = f"成功连接到浏览器窗口 {browser_id}"
        print(f"✅ {success_msg}")
        
        # 显示当前页面信息
        try:
            print(f"当前页面URL: {driver.url}")
            print(f"页面标题: {driver.title}")
        except Exception as e:
            print(f"获取页面信息时出错: {e}")
        
        return True, driver, success_msg
    else:
        error_msg = "浏览器窗口已打开，但连接失败"
        print(f"❌ {error_msg}")
        return False, None, error_msg

def batch_open_browsers(browser_ids, navigate_to_facebook=True, wait_between_opens=2):
    """
    批量打开多个比特浏览器窗口
    
    Args:
        browser_ids: 浏览器ID列表
        navigate_to_facebook: 是否导航到Facebook收件箱
        wait_between_opens: 每个窗口打开之间的等待时间（秒）
        
    Returns:
        dict: 批量操作结果
    """
    print(f"\n开始批量打开 {len(browser_ids)} 个比特浏览器窗口")
    
    results = []
    drivers = []
    success_count = 0
    failed_count = 0
    
    for i, browser_id in enumerate(browser_ids):
        print(f"\n正在打开第 {i+1}/{len(browser_ids)} 个窗口，ID: {browser_id}")
        
        success, driver, message = open_and_connect_browser(browser_id, navigate_to_facebook)
        
        results.append({
            'browser_id': browser_id,
            'success': success,
            'message': message,
            'driver': driver
        })
        
        if success:
            success_count += 1
            if driver:
                drivers.append(driver)
        else:
            failed_count += 1
        
        # 在打开下一个窗口前等待
        if i < len(browser_ids) - 1 and wait_between_opens > 0:
            print(f"等待 {wait_between_opens} 秒后打开下一个窗口")
            time.sleep(wait_between_opens)
    
    print(f"\n批量打开完成: 成功 {success_count} 个，失败 {failed_count} 个")
    
    return {
        'success_count': success_count,
        'failed_count': failed_count,
        'results': results,
        'drivers': drivers
    }

def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "="*60)
        print("独立比特浏览器窗口打开工具")
        print("="*60)
        print("1. 打开单个浏览器窗口")
        print("2. 批量打开多个浏览器窗口")
        print("3. 查看API配置信息")
        print("0. 退出")
        print("-"*60)
        
        choice = input("请选择功能 (0-3): ").strip()
        
        if choice == '1':
            browser_id = input("请输入比特浏览器窗口ID: ").strip()
            if browser_id:
                success, driver, message = open_and_connect_browser(browser_id)
                if success and driver:
                    print("浏览器已成功打开，可以进行后续操作")
            else:
                print("请输入有效的浏览器ID")
                
        elif choice == '2':
            browser_ids_input = input("请输入多个浏览器ID，用逗号分隔: ").strip()
            if browser_ids_input:
                browser_ids = [id.strip() for id in browser_ids_input.split(',')]
                batch_result = batch_open_browsers(browser_ids)
                
                print(f"\n详细结果:")
                for item in batch_result['results']:
                    browser_id = item['browser_id']
                    status = "✅ 成功" if item['success'] else "❌ 失败"
                    print(f"  {browser_id}: {status} - {item['message']}")
            else:
                print("请输入有效的浏览器ID列表")
                
        elif choice == '3':
            print(f"\n当前配置:")
            print(f"  比特浏览器API地址: {BITBROWSER_API_URL}")
            print(f"  DrissionPage可用: {'是' if DRISSIONPAGE_AVAILABLE else '否'}")
            print(f"  请确保比特浏览器API服务正在运行")
            
        elif choice == '0':
            print("退出程序")
            break
        else:
            print("无效选择，请重新输入")
        
        input("\n按回车键继续...")

def main():
    """主函数"""
    print("独立比特浏览器窗口打开工具")
    print(f"API地址: {BITBROWSER_API_URL}")
    print("请确保比特浏览器API服务正在运行")
    
    if not DRISSIONPAGE_AVAILABLE:
        print("\n注意: DrissionPage库未安装，只能打开浏览器窗口，无法进行页面操作")
        print("如需完整功能，请运行: pip install DrissionPage")
    
    # 检查是否有命令行参数
    import sys
    if len(sys.argv) > 1:
        browser_id = sys.argv[1]
        print(f"\n使用命令行参数打开浏览器: {browser_id}")
        success, driver, message = open_and_connect_browser(browser_id)
    else:
        # 启动交互式菜单
        interactive_menu()

if __name__ == "__main__":
    main()
