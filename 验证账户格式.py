#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证Oxylabs账户格式

根据您提供的curl命令验证正确的账户格式。

作者: AI助手
日期: 2025-01-02
"""

import requests

def test_account_format():
    """测试账户格式"""
    print("=" * 60)
    print("验证Oxylabs账户格式")
    print("=" * 60)
    
    # 从您的curl命令解析出的信息
    # curl 'https://ip.oxylabs.io/location' -U 'customer-z123456_bYIud-cc-US:*************' -x 'pr.oxylabs.io:7777'
    
    base_username = "z123456_bYIud"
    password = "Zzr442859970~"  # 您提供的密码
    proxy_host = "pr.oxylabs.io"
    proxy_port = "7777"
    
    print(f"基础账户: {base_username}")
    print(f"密码: {'*' * len(password)}")
    print(f"代理服务器: {proxy_host}:{proxy_port}")
    print("-" * 60)
    
    # 测试不同的格式
    test_formats = [
        {
            "name": "基础格式（无城市）",
            "username": f"customer-{base_username}",
            "description": "不指定城市，使用随机美国IP"
        },
        {
            "name": "纽约格式",
            "username": f"customer-{base_username}-cc-US-city-new_york",
            "description": "指定纽约城市"
        },
        {
            "name": "洛杉矶格式", 
            "username": f"customer-{base_username}-cc-US-city-los_angeles",
            "description": "指定洛杉矶城市"
        },
        {
            "name": "芝加哥格式",
            "username": f"customer-{base_username}-cc-US-city-chicago", 
            "description": "指定芝加哥城市"
        }
    ]
    
    for i, test_format in enumerate(test_formats, 1):
        print(f"\n{i}. {test_format['name']}")
        print(f"   描述: {test_format['description']}")
        print(f"   用户名: {test_format['username']}")
        print("   测试连接...", end=" ")
        
        # 设置代理
        proxies = {
            'http': f'http://{test_format["username"]}:{password}@{proxy_host}:{proxy_port}',
            'https': f'http://{test_format["username"]}:{password}@{proxy_host}:{proxy_port}'
        }
        
        try:
            response = requests.get(
                'https://ip.oxylabs.io/location',
                proxies=proxies,
                timeout=15
            )
            
            if response.status_code == 200:
                location_data = response.json()
                print("✅ 成功")
                print(f"   IP: {location_data.get('ip', 'N/A')}")
                print(f"   国家: {location_data.get('country', 'N/A')}")
                print(f"   城市: {location_data.get('city', 'N/A')}")
                print(f"   地区: {location_data.get('region', 'N/A')}")
                
                # 生成对应的curl命令
                curl_cmd = f"curl 'https://ip.oxylabs.io/location' -U '{test_format['username']}:{password}' -x '{proxy_host}:{proxy_port}'"
                print(f"   curl命令: {curl_cmd}")
                
            else:
                print(f"❌ 失败 (HTTP {response.status_code})")
                if response.status_code == 407:
                    print("   错误: 代理认证失败，请检查账户和密码")
                elif response.status_code == 403:
                    print("   错误: 访问被拒绝，可能账户权限不足")
                else:
                    print(f"   响应: {response.text[:100]}")
                    
        except requests.exceptions.ProxyError as e:
            print("❌ 代理错误")
            print(f"   详细错误: {e}")
            
        except requests.exceptions.Timeout:
            print("❌ 连接超时")
            
        except Exception as e:
            print(f"❌ 其他错误: {e}")

def show_configuration_summary():
    """显示配置总结"""
    print("\n" + "=" * 60)
    print("配置总结")
    print("=" * 60)
    
    base_username = "z123456_bYIud"
    password = "Zzr442859970~"
    
    print("✅ 已更新的文件:")
    print("   1. 比特浏览器美国城市代理工具.py")
    print("   2. 比特浏览器美国城市代理工具_命令行版.py")
    print("   3. 测试比特浏览器API修复.py")
    print("   4. 测试Oxylabs账户.py")
    
    print(f"\n📝 账户配置:")
    print(f"   基础账户: {base_username}")
    print(f"   密码: {'*' * len(password)}")
    print(f"   代理服务器: pr.oxylabs.io:7777")
    
    print(f"\n🌐 用户名格式:")
    print(f"   基础格式: customer-{base_username}")
    print(f"   城市格式: customer-{base_username}-cc-US-city-{{city_name}}")
    
    print(f"\n🎯 示例:")
    cities = ["new_york", "los_angeles", "chicago", "houston"]
    for city in cities:
        print(f"   {city.replace('_', ' ').title()}: customer-{base_username}-cc-US-city-{city}")

def main():
    """主函数"""
    try:
        test_account_format()
        show_configuration_summary()
        
        print("\n" + "=" * 60)
        print("验证完成")
        print("=" * 60)
        print("现在您可以使用修复后的工具，账户信息已正确配置！")
        
    except KeyboardInterrupt:
        print("\n验证被用户中断")
    except Exception as e:
        print(f"\n验证过程中出现错误: {e}")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
