#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
强制清理BitBrowser窗口

当API显示有窗口但列表为空时，尝试强制清理
"""

import requests
import json
import time

def force_clear_browsers():
    """强制清理所有浏览器窗口"""
    print("=" * 60)
    print("强制清理BitBrowser窗口")
    print("=" * 60)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        # 1. 检查当前状态
        print("1. 检查当前窗口状态...")
        list_data = {"page": 1, "pageSize": 100}
        response = requests.post(f"{url}/browser/list",
                               data=json.dumps(list_data),
                               headers=headers,
                               timeout=30)
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                total_num = result['data'].get('totalNum', 0)
                browsers = result['data'].get('list', [])
                
                print(f"API报告总窗口数: {total_num}")
                print(f"实际列表窗口数: {len(browsers)}")
                
                if total_num > 0 and len(browsers) == 0:
                    print("⚠️ 检测到隐藏窗口，尝试强制清理...")
                    
                    # 2. 尝试创建一个测试窗口来触发清理
                    print("\n2. 创建测试窗口触发系统清理...")
                    test_data = {
                        'name': 'force_clear_test',
                        'remark': '强制清理测试',
                        'proxyMethod': 2,
                        'proxyType': 'noproxy',
                        'browserFingerPrint': {}
                    }
                    
                    create_response = requests.post(f"{url}/browser/update",
                                                  data=json.dumps(test_data),
                                                  headers=headers,
                                                  timeout=30)
                    
                    print(f"创建测试窗口响应: {create_response.status_code}")
                    print(f"响应内容: {create_response.text}")
                    
                    if create_response.status_code == 200:
                        create_result = create_response.json()
                        if create_result.get('success'):
                            test_id = create_result['data']['id']
                            print(f"✅ 测试窗口创建成功: {test_id}")
                            
                            # 立即删除测试窗口
                            delete_data = {"id": test_id}
                            delete_response = requests.post(f"{url}/browser/delete",
                                                          data=json.dumps(delete_data),
                                                          headers=headers,
                                                          timeout=30)
                            print(f"删除测试窗口: {delete_response.status_code}")
                            
                            # 3. 重新检查状态
                            print("\n3. 重新检查窗口状态...")
                            time.sleep(2)
                            
                            check_response = requests.post(f"{url}/browser/list",
                                                         data=json.dumps(list_data),
                                                         headers=headers,
                                                         timeout=30)
                            
                            if check_response.status_code == 200:
                                check_result = check_response.json()
                                if check_result.get('success'):
                                    new_total = check_result['data'].get('totalNum', 0)
                                    new_browsers = check_result['data'].get('list', [])
                                    
                                    print(f"清理后总窗口数: {new_total}")
                                    print(f"清理后列表窗口数: {len(new_browsers)}")
                                    
                                    if new_total == 0:
                                        print("✅ 强制清理成功！现在可以创建新窗口了")
                                        return True
                                    else:
                                        print("⚠️ 仍有窗口存在，可能需要手动清理")
                        else:
                            error_msg = create_result.get('msg', '未知错误')
                            if "已超过套餐窗口数" in error_msg:
                                print("❌ 仍然超过套餐窗口数，需要其他方法清理")
                                
                                # 4. 尝试重启BitBrowser的建议
                                print("\n建议解决方案:")
                                print("1. 完全关闭BitBrowser应用程序")
                                print("2. 等待10秒")
                                print("3. 重新启动BitBrowser")
                                print("4. 重新运行此工具")
                                
                                return False
                            else:
                                print(f"❌ 创建测试窗口失败: {error_msg}")
                    else:
                        print(f"❌ 创建测试窗口请求失败: {create_response.status_code}")
                        
                elif len(browsers) > 0:
                    print("✅ 找到可见窗口，可以正常删除")
                    for i, browser in enumerate(browsers, 1):
                        browser_id = browser.get('id')
                        browser_name = browser.get('name', 'N/A')
                        print(f"  {i}. {browser_name} (ID: {browser_id})")
                    
                    confirm = input(f"\n是否删除这 {len(browsers)} 个窗口？(y/n): ").strip().lower()
                    if confirm == 'y':
                        success_count = 0
                        for browser in browsers:
                            browser_id = browser.get('id')
                            if browser_id:
                                delete_data = {"id": browser_id}
                                delete_response = requests.post(f"{url}/browser/delete",
                                                              data=json.dumps(delete_data),
                                                              headers=headers,
                                                              timeout=30)
                                if delete_response.status_code == 200:
                                    delete_result = delete_response.json()
                                    if delete_result.get('success'):
                                        success_count += 1
                                        print(f"✅ 删除成功: {browser.get('name', 'N/A')}")
                                    else:
                                        print(f"❌ 删除失败: {delete_result.get('msg', '未知错误')}")
                                time.sleep(1)
                        
                        print(f"\n✅ 删除完成: {success_count}/{len(browsers)} 个窗口")
                        return success_count > 0
                else:
                    print("✅ 没有窗口需要清理")
                    return True
            else:
                print(f"❌ API调用失败: {result.get('msg', '未知错误')}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 强制清理失败: {e}")
        import traceback
        traceback.print_exc()
    
    return False

def test_create_after_clear():
    """清理后测试创建新窗口"""
    print("\n" + "=" * 60)
    print("测试创建新窗口")
    print("=" * 60)
    
    url = "http://127.0.0.1:54345"
    headers = {'Content-Type': 'application/json'}
    
    try:
        test_data = {
            'name': 'test_after_clear',
            'remark': '清理后测试',
            'proxyMethod': 2,
            'proxyType': 'noproxy',
            'browserFingerPrint': {}
        }
        
        response = requests.post(f"{url}/browser/update",
                               data=json.dumps(test_data),
                               headers=headers,
                               timeout=30)
        
        print(f"创建响应状态码: {response.status_code}")
        print(f"创建响应内容: {response.text}")
        
        if response.status_code == 200:
            result = response.json()
            if result.get('success'):
                browser_id = result['data']['id']
                print(f"✅ 测试窗口创建成功: {browser_id}")
                
                # 清理测试窗口
                delete_data = {"id": browser_id}
                requests.post(f"{url}/browser/delete",
                            data=json.dumps(delete_data),
                            headers=headers,
                            timeout=30)
                print("测试窗口已清理")
                
                return True
            else:
                error_msg = result.get('msg', '未知错误')
                print(f"❌ 创建失败: {error_msg}")
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
    
    return False

def main():
    """主函数"""
    print("🧹 BitBrowser强制清理工具")
    print("解决API显示有窗口但列表为空的问题")
    
    # 执行强制清理
    clear_success = force_clear_browsers()
    
    if clear_success:
        print("\n✅ 清理完成")
        
        # 测试创建新窗口
        test_create = input("\n是否测试创建新窗口？(y/n): ").strip().lower()
        if test_create == 'y':
            test_success = test_create_after_clear()
            if test_success:
                print("\n🎉 完美！现在可以正常使用BitBrowser了")
            else:
                print("\n❌ 测试创建失败，可能还有其他问题")
    else:
        print("\n❌ 清理失败")
        print("\n手动解决方案:")
        print("1. 完全关闭BitBrowser应用程序")
        print("2. 清理BitBrowser的临时文件和缓存")
        print("3. 重新启动BitBrowser")
        print("4. 检查BitBrowser的窗口管理界面")
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
