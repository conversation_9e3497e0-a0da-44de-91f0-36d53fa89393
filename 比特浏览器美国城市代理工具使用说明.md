# 比特浏览器美国城市代理工具使用说明

## 概述

这个工具集成了美国城市选择器和比特浏览器API，可以自动选择美国城市并使用对应的Oxylabs代理IP创建和打开比特浏览器窗口。

## 📁 文件说明

### 1. **比特浏览器美国城市代理工具.py** - 图形界面版本
- 🖥️ 友好的GUI界面，包含配置、操作、日志三个标签页
- 🔧 可视化配置Oxylabs代理信息
- 🏙️ 图形化选择美国城市
- 🌐 一键创建和打开浏览器窗口
- 📋 实时日志显示

### 2. **比特浏览器美国城市代理工具_命令行版.py** - 命令行版本
- ⌨️ 命令行交互界面
- 🔧 命令行配置代理信息
- 🏙️ 搜索和选择美国城市
- 🌐 批量操作浏览器窗口

## 🚀 快速开始

### 前置条件

1. **比特浏览器**: 确保比特浏览器客户端已安装并运行
2. **Oxylabs账户**: 需要有效的Oxylabs代理服务账户
3. **Python环境**: Python 3.6+ 和 requests 库

### 安装依赖

```bash
pip install requests
```

### 图形界面版本使用

```bash
python 比特浏览器美国城市代理工具.py
```

**操作步骤**:
1. **配置标签页**: 输入Oxylabs用户名、密码和代理服务器信息
2. **操作标签页**: 搜索并选择美国城市
3. 点击"创建/更新浏览器"按钮
4. 点击"打开浏览器"按钮
5. **日志标签页**: 查看操作日志和错误信息

### 命令行版本使用

```bash
python 比特浏览器美国城市代理工具_命令行版.py
```

**基本命令流程**:
```bash
> config                    # 配置Oxylabs代理信息
> test                      # 测试比特浏览器API连接
> search new york           # 搜索纽约相关城市
> select 1                  # 选择第1个城市
> create                    # 创建浏览器窗口
> open <浏览器ID>           # 打开浏览器窗口
```

## 🔧 配置说明

### Oxylabs代理配置

| 配置项 | 说明 | 示例 |
|--------|------|------|
| 用户名 | Oxylabs账户用户名 | `your_username` |
| 密码 | Oxylabs账户密码 | `your_password` |
| 代理主机 | 代理服务器地址 | `pr.oxylabs.io` |
| 代理端口 | 代理服务器端口 | `7777` |

### 比特浏览器配置

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| API地址 | 比特浏览器本地API地址 | `http://127.0.0.1:54345` |
| 窗口名称 | 创建的浏览器窗口名称 | `US_City_Browser` |
| 内核版本 | 浏览器内核版本 | `124` |

## 🏙️ 支持的美国城市

工具包含美国25个州的主要城市，包括：

### 热门州和城市
- **加利福尼亚州**: Los Angeles, San Francisco, San Diego, San Jose, Sacramento
- **纽约州**: New York, Buffalo, Rochester, Albany, Syracuse
- **德克萨斯州**: Houston, San Antonio, Dallas, Austin, Fort Worth
- **佛罗里达州**: Jacksonville, Miami, Tampa, Orlando, St. Petersburg
- **伊利诺伊州**: Chicago, Aurora, Rockford, Joliet, Naperville

### 代理参数生成规则

城市名称会自动转换为Oxylabs代理参数格式：

| 城市名称 | 生成参数 | 完整代理用户名 |
|---------|---------|---------------|
| New York | `cc-US-city-new_york` | `customer-USERNAME-cc-US-city-new_york` |
| Los Angeles | `cc-US-city-los_angeles` | `customer-USERNAME-cc-US-city-los_angeles` |
| San Francisco | `cc-US-city-san_francisco` | `customer-USERNAME-cc-US-city-san_francisco` |

## 🌐 工作流程

### 1. 代理配置生成
```
选择城市: "New York, New York"
↓
生成参数: "cc-US-city-new_york"
↓
代理用户名: "customer-your_username-cc-US-city-new_york"
```

### 2. 浏览器窗口创建
```
API调用: POST /browser/update
↓
配置代理: HTTP代理 + 城市参数
↓
返回浏览器ID: "abc123def456..."
```

### 3. 浏览器窗口打开
```
API调用: POST /browser/open
↓
返回连接信息: WebSocket地址 + HTTP调试地址
↓
浏览器窗口启动: 使用指定城市的IP地址
```

## 📋 命令行版本命令参考

### 配置命令
- `config` - 配置Oxylabs代理账户信息
- `test` - 测试比特浏览器API连接
- `status` - 显示当前配置状态

### 城市选择命令
- `search <关键词>` - 搜索美国城市
- `list` - 显示所有可用城市
- `select <编号>` - 选择城市

### 浏览器操作命令
- `create` - 创建/更新浏览器窗口（使用选定城市的代理）
- `open <浏览器ID>` - 打开浏览器窗口
- `close <浏览器ID>` - 关闭浏览器窗口

### 其他命令
- `help` - 显示帮助信息
- `clear` - 清屏
- `quit` / `exit` - 退出程序

## 🔍 使用示例

### 完整操作示例

```bash
# 启动程序
python 比特浏览器美国城市代理工具_命令行版.py

# 配置代理信息
> config
请输入Oxylabs用户名: your_username
请输入Oxylabs密码: your_password
代理主机 (当前: pr.oxylabs.io): 
代理端口 (当前: 7777): 
✅ 代理配置已更新

# 测试API连接
> test
🔍 测试比特浏览器API连接...
✅ 比特浏览器API连接成功

# 搜索城市
> search new york
🏙️ 找到 5 个城市:
--------------------------------------------------
  1. New York, New York
  2. Buffalo, New York
  3. Rochester, New York
  4. Albany, New York
  5. Syracuse, New York

# 选择城市
> select 1
✅ 已选择城市: New York, New York

# 创建浏览器窗口
> create
🔧 创建浏览器窗口...
📍 城市: New York, New York
🌐 代理参数: cc-US-city-new_york
👤 代理用户名: customer-your_username-cc-US-city-new_york
✅ 浏览器窗口创建成功!
🆔 浏览器ID: abc123def456ghi789
是否立即打开浏览器窗口? (y/N): y
🚀 正在打开浏览器窗口: abc123def456ghi789
✅ 浏览器窗口打开成功!
🔗 调试地址: http://127.0.0.1:9222
🌐 WebSocket地址: ws://127.0.0.1:9222/devtools/browser/...
```

## ⚠️ 注意事项

### 1. 比特浏览器要求
- 确保比特浏览器客户端正在运行
- 默认API端口为54345，如有修改请在配置中更新
- 确保有足够的浏览器窗口配额

### 2. Oxylabs代理要求
- 需要有效的Oxylabs住宅代理账户
- 确保账户有足够的流量配额
- 代理用户名格式必须正确

### 3. 网络要求
- 确保能够访问Oxylabs代理服务器
- 确保本地API端口未被占用
- 防火墙需要允许相关端口通信

## 🛠️ 故障排除

### 常见问题

#### 1. API连接失败
**问题**: 测试比特浏览器API连接失败
**解决方案**:
- 检查比特浏览器是否正在运行
- 确认API地址和端口是否正确
- 检查防火墙设置

#### 2. 代理连接失败
**问题**: 浏览器窗口创建成功但无法使用代理
**解决方案**:
- 检查Oxylabs账户凭据是否正确
- 确认账户是否有足够的流量配额
- 检查代理服务器地址和端口

#### 3. 浏览器窗口打开失败
**问题**: 创建成功但无法打开浏览器窗口
**解决方案**:
- 检查浏览器ID是否正确
- 确认比特浏览器客户端状态
- 查看错误日志获取详细信息

#### 4. 城市代理参数错误
**问题**: 生成的代理参数格式不正确
**解决方案**:
- 检查城市名称是否包含特殊字符
- 确认代理参数转换规则
- 手动验证生成的参数格式

## 📞 技术支持

如果遇到问题，请检查：

1. **环境要求**: Python 3.6+, requests库
2. **服务状态**: 比特浏览器和Oxylabs服务是否正常
3. **配置信息**: 代理账户和API地址是否正确
4. **网络连接**: 是否能正常访问相关服务

## 📄 许可证

本工具仅供学习和个人使用。使用时请遵守：
- 比特浏览器服务条款
- Oxylabs代理服务条款
- 相关法律法规

---

**提示**: 使用前请确保您有合法的代理服务账户，并遵守相关的使用条款和法律法规。
