#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
比特浏览器美国城市代理工具

这个工具结合了美国城市选择器和比特浏览器API，
可以选择美国城市并使用对应的Oxylabs代理IP打开比特浏览器窗口。

功能：
- 选择美国城市
- 配置Oxylabs代理
- 创建/更新比特浏览器窗口
- 打开浏览器窗口

使用方法：
1. 配置Oxylabs代理账户信息
2. 选择美国城市
3. 输入比特浏览器窗口ID（可选，会自动创建）
4. 打开浏览器窗口

作者: AI助手
日期: 2025-01-02
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import requests
import json
import time
import re
import threading
import random
from threading import Thread, Timer
from concurrent.futures import ThreadPoolExecutor, as_completed
from verified_cities import VERIFIED_US_CITIES

# 比特浏览器风格颜色配置
class BitBrowserColors:
    PRIMARY = "#4A90E2"      # 主蓝色
    PRIMARY_DARK = "#357ABD"  # 深蓝色
    BACKGROUND = "#F8F9FA"    # 背景灰
    CARD_BG = "#FFFFFF"       # 卡片背景
    TEXT_PRIMARY = "#333333"  # 主文字
    TEXT_SECONDARY = "#6C757D" # 次要文字
    SUCCESS = "#28A745"       # 成功绿
    WARNING = "#FFC107"       # 警告黄
    DANGER = "#DC3545"        # 危险红
    BORDER = "#DEE2E6"        # 边框色

class BitBrowserUSCityTool:
    def __init__(self, root):
        self.root = root
        self.root.title("比特浏览器美国城市代理工具")
        self.root.geometry("1200x800")
        self.root.resizable(True, True)

        # 设置窗口图标（如果有的话）
        try:
            self.root.iconbitmap(default="icon.ico")
        except:
            pass
        
        # 比特浏览器API配置
        self.bit_api_url = "http://127.0.0.1:54345"
        self.bit_headers = {'Content-Type': 'application/json'}

        # 常见的比特浏览器端口
        self.common_ports = ["54345", "54346", "54347", "54348", "54349"]
        
        # Oxylabs代理配置
        self.oxylabs_proxy_host = "pr.oxylabs.io"
        self.oxylabs_proxy_port = "7777"

        # 自动更换IP相关变量
        self.auto_change_enabled = False
        self.auto_change_interval = 30  # 默认30分钟
        self.auto_change_timer = None
        self.countdown_timer = None
        self.remaining_time = 0

        # 初始化变量
        self.selected_city = None
        self.city_mapping = {}
        self.filtered_cities = []

        # 验证通过的美国城市列表（IP精确匹配，按州分组）
        self.us_cities = VERIFIED_US_CITIES
        
        # 创建所有城市的平面列表（中文显示，英文用于API）
        self.all_cities = []
        self.city_mapping = {}  # 中文显示名 -> 英文API名的映射

        for state_cn, cities in self.us_cities.items():
            for city_cn, city_en in cities:
                display_name = f"{city_cn}, {state_cn}"
                self.all_cities.append(display_name)
                self.city_mapping[display_name] = city_en

        self.filtered_cities = self.all_cities.copy()
        self.selected_city = None
        self.current_browser_id = None

        # 会话时间设置（分钟）
        self.session_time = 10  # 默认10分钟

        # 设置保存文件
        self.settings_file = "bitbrowser_settings.json"

        self.create_widgets()

        # 在GUI创建后加载保存的设置
        self.load_settings()
        
    def create_widgets(self):
        # 设置窗口样式和主题（比特浏览器风格）
        self.root.configure(bg=BitBrowserColors.BACKGROUND)

        # 配置样式
        style = ttk.Style()
        try:
            style.theme_use('clam')  # 使用现代主题
        except:
            pass  # 如果主题不可用，使用默认主题

        # 比特浏览器风格样式配置
        style.configure("Title.TLabel",
                       font=("Microsoft YaHei", 18, "bold"),
                       foreground=BitBrowserColors.PRIMARY,
                       background=BitBrowserColors.BACKGROUND)

        style.configure("Subtitle.TLabel",
                       font=("Microsoft YaHei", 10),
                       foreground=BitBrowserColors.TEXT_SECONDARY,
                       background=BitBrowserColors.BACKGROUND)

        style.configure("TLabelFrame",
                       background=BitBrowserColors.CARD_BG,
                       borderwidth=1,
                       relief="solid")

        style.configure("TLabelFrame.Label",
                       font=("Microsoft YaHei", 11, "bold"),
                       foreground=BitBrowserColors.PRIMARY,
                       background=BitBrowserColors.CARD_BG)

        style.configure("Primary.TButton",
                       font=("Microsoft YaHei", 10, "bold"),
                       foreground="white",
                       background=BitBrowserColors.PRIMARY)

        style.configure("Success.TButton",
                       font=("Microsoft YaHei", 10, "bold"),
                       foreground="white",
                       background=BitBrowserColors.SUCCESS)

        style.configure("Warning.TButton",
                       font=("Microsoft YaHei", 10, "bold"),
                       foreground="white",
                       background=BitBrowserColors.WARNING)

        # 配置其他组件样式
        style.configure("TLabel",
                       font=("Microsoft YaHei", 9),
                       background=BitBrowserColors.CARD_BG,
                       foreground=BitBrowserColors.TEXT_PRIMARY)

        style.configure("TEntry",
                       font=("Microsoft YaHei", 9),
                       fieldbackground="white",
                       borderwidth=1)

        style.configure("TButton",
                       font=("Microsoft YaHei", 9))

        # 创建主标题区域（比特浏览器风格）
        title_frame = tk.Frame(self.root, bg=BitBrowserColors.BACKGROUND, height=80)
        title_frame.pack(fill=tk.X, padx=20, pady=(20, 15))
        title_frame.pack_propagate(False)

        # 主标题
        title_label = ttk.Label(title_frame, text="🌐 比特浏览器美国城市代理工具",
                               style="Title.TLabel")
        title_label.pack(pady=(10, 5))

        # 副标题
        subtitle_label = ttk.Label(title_frame, text="智能代理管理 | 城市精准定位 | 自动更换IP",
                                  style="Subtitle.TLabel")
        subtitle_label.pack()

        # 分隔线
        separator1 = ttk.Separator(self.root, orient='horizontal')
        separator1.pack(fill=tk.X, padx=20, pady=(5, 15))

        # 创建主要内容区域（左右分栏，比特浏览器风格）
        main_frame = tk.Frame(self.root, bg=BitBrowserColors.BACKGROUND)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)

        # 左侧操作区域（卡片风格）
        left_frame = ttk.LabelFrame(main_frame, text="🎯 操作控制", padding="20")
        left_frame.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=(0, 10))

        # 右侧城市选择区域（卡片风格）
        right_frame = ttk.LabelFrame(main_frame, text="🌍 城市选择", padding="20")
        right_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=(10, 0))

        # 分隔线
        separator2 = ttk.Separator(self.root, orient='horizontal')
        separator2.pack(fill=tk.X, padx=20, pady=(15, 10))

        # 底部日志区域（卡片风格）
        log_frame = ttk.LabelFrame(self.root, text="📋 操作日志", padding="15")
        log_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=(5, 20))

        self.create_operation_area(left_frame)
        self.create_city_area(right_frame)
        self.create_log_area(log_frame)
        
    def create_operation_area(self, parent):
        """创建左侧操作区域"""
        # 城市选择框架
        city_frame = ttk.LabelFrame(parent, text="🏙️ 选择美国城市", padding="10")
        city_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 10))

        # 搜索框
        search_frame = ttk.Frame(city_frame)
        search_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(search_frame, text="🔍 搜索城市:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=25, font=("Microsoft YaHei", 9))
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        clear_btn = ttk.Button(search_frame, text="清除", command=self.clear_search, width=6)
        clear_btn.pack(side=tk.RIGHT)

        # 城市列表
        list_frame = ttk.Frame(city_frame)
        list_frame.pack(fill=tk.BOTH, expand=True)

        self.city_listbox = tk.Listbox(list_frame, height=12, font=("Microsoft YaHei", 9),
                                      selectmode=tk.SINGLE, activestyle='dotbox')
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.city_listbox.yview)
        self.city_listbox.configure(yscrollcommand=scrollbar.set)

        self.city_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.city_listbox.bind('<<ListboxSelect>>', self.on_city_select)

        # 选中城市显示
        city_status_frame = ttk.LabelFrame(parent, text="📍 当前选择", padding="10")
        city_status_frame.pack(fill=tk.X, pady=(0, 10))

        self.selected_city_label = ttk.Label(city_status_frame, text="❌ 未选择城市",
                                           font=("Microsoft YaHei", 10, "bold"), foreground="red")
        self.selected_city_label.pack()

        # 浏览器ID配置
        id_frame = ttk.LabelFrame(parent, text="🌐 浏览器设置", padding="10")
        id_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(id_frame, text="浏览器ID:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.browser_id_var = tk.StringVar()
        id_entry = ttk.Entry(id_frame, textvariable=self.browser_id_var, width=25, font=("Microsoft YaHei", 9))
        id_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        ttk.Label(id_frame, text="(留空自动创建)", font=("Microsoft YaHei", 8), foreground="gray").pack(side=tk.LEFT)

        # 操作按钮（美化版）
        btn_frame = ttk.LabelFrame(parent, text="🚀 操作控制", padding="10")
        btn_frame.pack(fill=tk.X)

        # 创建按钮样式
        style = ttk.Style()
        style.configure("Action.TButton", font=("Microsoft YaHei", 10, "bold"))

        self.create_btn = ttk.Button(btn_frame, text="🔄 修改当前IP",
                                   command=self.create_or_update_browser,
                                   style="Primary.TButton", width=15, state=tk.DISABLED)
        self.create_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        self.open_btn = ttk.Button(btn_frame, text="🌐 打开浏览器",
                                 command=self.open_browser, state=tk.DISABLED,
                                 style="Success.TButton", width=15)
        self.open_btn.pack(side=tk.LEFT, padx=(0, 10), pady=5)

        self.close_btn = ttk.Button(btn_frame, text="❌ 关闭浏览器",
                                  command=self.close_browser, state=tk.DISABLED,
                                  style="Warning.TButton", width=15)
        self.close_btn.pack(side=tk.LEFT, pady=5)

        # 初始化城市列表
        self.update_city_list()

    def create_config_area(self, parent):
        """创建右侧配置区域"""
        # Oxylabs代理配置框架
        oxylabs_frame = ttk.LabelFrame(parent, text="🔐 Oxylabs代理配置", padding="10")
        oxylabs_frame.pack(fill=tk.X, pady=(0, 10))

        # 账户信息
        account_frame = ttk.Frame(oxylabs_frame)
        account_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(account_frame, text="👤 用户名:", font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.username_var = tk.StringVar(value="z123456_bYIud")
        username_entry = ttk.Entry(account_frame, textvariable=self.username_var, width=20, font=("Microsoft YaHei", 9))
        username_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        ttk.Label(account_frame, text="🔑 密码:", font=("Microsoft YaHei", 9)).grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.password_var = tk.StringVar(value="Zzr442859970~")
        password_entry = ttk.Entry(account_frame, textvariable=self.password_var, show="*", width=20, font=("Microsoft YaHei", 9))
        password_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(5, 0))

        account_frame.columnconfigure(1, weight=1)

        # 会话时间设置
        session_frame = ttk.Frame(oxylabs_frame)
        session_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(session_frame, text="⏱️ 会话时间:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.session_time_var = tk.IntVar(value=self.session_time)

        time_control_frame = ttk.Frame(session_frame)
        time_control_frame.pack(side=tk.LEFT, fill=tk.X, expand=True)

        session_scale = ttk.Scale(time_control_frame, from_=1, to=30, orient=tk.HORIZONTAL,
                                variable=self.session_time_var, length=120)
        session_scale.pack(side=tk.LEFT, padx=(0, 5))

        self.session_label = ttk.Label(time_control_frame, text=f"{self.session_time}分钟", font=("Microsoft YaHei", 9))
        self.session_label.pack(side=tk.LEFT)

        session_scale.configure(command=self.on_session_time_change)

        # 代理服务器配置
        proxy_frame = ttk.Frame(oxylabs_frame)
        proxy_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(proxy_frame, text="🌐 代理主机:", font=("Microsoft YaHei", 9)).grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.proxy_host_var = tk.StringVar(value=self.oxylabs_proxy_host)
        host_entry = ttk.Entry(proxy_frame, textvariable=self.proxy_host_var, width=20, font=("Microsoft YaHei", 9))
        host_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), padx=(0, 10))

        ttk.Label(proxy_frame, text="🔌 代理端口:", font=("Microsoft YaHei", 9)).grid(row=1, column=0, sticky=tk.W, padx=(0, 5), pady=(5, 0))
        self.proxy_port_var = tk.StringVar(value=self.oxylabs_proxy_port)
        port_entry = ttk.Entry(proxy_frame, textvariable=self.proxy_port_var, width=20, font=("Microsoft YaHei", 9))
        port_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=(5, 0))

        proxy_frame.columnconfigure(1, weight=1)

        # 比特浏览器配置框架
        bit_frame = ttk.LabelFrame(parent, text="🖥️ 比特浏览器配置", padding="10")
        bit_frame.pack(fill=tk.X, pady=(0, 10))

        # API地址
        api_frame = ttk.Frame(bit_frame)
        api_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(api_frame, text="🔗 API地址:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.bit_api_var = tk.StringVar(value=self.bit_api_url)
        api_entry = ttk.Entry(api_frame, textvariable=self.bit_api_var, width=25, font=("Microsoft YaHei", 9))
        api_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        test_btn = ttk.Button(api_frame, text="🔍 测试", command=self.test_bit_connection, width=8)
        test_btn.pack(side=tk.RIGHT)

        # 自动更换IP设置
        auto_frame = ttk.LabelFrame(parent, text="🔄 自动更换IP", padding="10")
        auto_frame.pack(fill=tk.X, pady=(0, 10))

        # 启用开关
        switch_frame = ttk.Frame(auto_frame)
        switch_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(switch_frame, text="🔘 启用自动更换:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.auto_change_var = tk.BooleanVar(value=False)
        auto_check = ttk.Checkbutton(switch_frame, variable=self.auto_change_var,
                                   command=self.toggle_auto_change)
        auto_check.pack(side=tk.LEFT)

        # 时间间隔设置
        interval_frame = ttk.Frame(auto_frame)
        interval_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(interval_frame, text="⏰ 更换间隔:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.interval_var = tk.IntVar(value=self.auto_change_interval)
        interval_spin = ttk.Spinbox(interval_frame, from_=5, to=120, textvariable=self.interval_var,
                                  width=8, font=("Microsoft YaHei", 9))
        interval_spin.pack(side=tk.LEFT, padx=(0, 5))
        ttk.Label(interval_frame, text="分钟", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT)

        # 状态显示
        status_frame = ttk.Frame(auto_frame)
        status_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(status_frame, text="📊 当前状态:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.auto_status_label = ttk.Label(status_frame, text="❌ 已停止",
                                         font=("Microsoft YaHei", 9), foreground="red")
        self.auto_status_label.pack(side=tk.LEFT)

        # 倒计时显示
        countdown_frame = ttk.Frame(auto_frame)
        countdown_frame.pack(fill=tk.X, pady=(0, 10))

        ttk.Label(countdown_frame, text="⏳ 下次更换:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.countdown_label = ttk.Label(countdown_frame, text="--:--",
                                       font=("Microsoft YaHei", 9), foreground="blue")
        self.countdown_label.pack(side=tk.LEFT)

        # 控制按钮
        control_frame = ttk.Frame(auto_frame)
        control_frame.pack(fill=tk.X)

        self.start_auto_btn = ttk.Button(control_frame, text="▶️ 开始",
                                       command=self.start_auto_change,
                                       style="Success.TButton", width=8)
        self.start_auto_btn.pack(side=tk.LEFT, padx=(0, 5))

        self.stop_auto_btn = ttk.Button(control_frame, text="⏹️ 停止",
                                      command=self.stop_auto_change,
                                      style="Warning.TButton", width=8, state=tk.DISABLED)
        self.stop_auto_btn.pack(side=tk.LEFT)

        # 设置管理
        settings_frame = ttk.LabelFrame(parent, text="💾 设置管理", padding="10")
        settings_frame.pack(fill=tk.X)

        btn_frame = ttk.Frame(settings_frame)
        btn_frame.pack(fill=tk.X)

        save_button = ttk.Button(btn_frame, text="💾 保存设置", command=self.save_settings,
                               style="Primary.TButton", width=12)
        save_button.pack(side=tk.LEFT, padx=(0, 10))

        load_button = ttk.Button(btn_frame, text="📂 加载设置", command=self.load_settings,
                               style="Primary.TButton", width=12)
        load_button.pack(side=tk.LEFT)

        # 浏览器设置（简化版，用户不需要配置窗口名称）
        # 使用固定的浏览器窗口名称
        self.browser_name_var = tk.StringVar(value="US_City_Proxy_Browser")

    def create_city_area(self, parent):
        """创建右侧城市选择区域"""
        # 搜索框
        search_frame = ttk.Frame(parent)
        search_frame.pack(fill=tk.X, pady=(0, 15))

        ttk.Label(search_frame, text="🔍 搜索城市:", font=("Microsoft YaHei", 9)).pack(side=tk.LEFT, padx=(0, 5))
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(search_frame, textvariable=self.search_var, width=25, font=("Microsoft YaHei", 9))
        search_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 5))

        clear_btn = ttk.Button(search_frame, text="清除", command=self.clear_search, width=6)
        clear_btn.pack(side=tk.RIGHT)

        # 城市列表
        list_frame = ttk.Frame(parent)
        list_frame.pack(fill=tk.BOTH, expand=True, pady=(0, 15))

        self.city_listbox = tk.Listbox(list_frame, height=15, font=("Microsoft YaHei", 9),
                                      selectmode=tk.SINGLE, activestyle='dotbox')
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.city_listbox.yview)
        self.city_listbox.configure(yscrollcommand=scrollbar.set)

        self.city_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)

        self.city_listbox.bind('<<ListboxSelect>>', self.on_city_select)

        # 选中城市显示
        city_status_frame = ttk.LabelFrame(parent, text="📍 当前选择", padding="10")
        city_status_frame.pack(fill=tk.X)

        self.selected_city_label = ttk.Label(city_status_frame, text="❌ 未选择城市",
                                           font=("Microsoft YaHei", 10, "bold"), foreground="red")
        self.selected_city_label.pack()

    def create_log_area(self, parent):
        """创建底部日志区域"""
        # 日志控制框架
        log_control_frame = ttk.Frame(parent)
        log_control_frame.pack(fill=tk.X, pady=(0, 5))

        ttk.Label(log_control_frame, text="📝 实时日志输出", font=("Microsoft YaHei", 10, "bold")).pack(side=tk.LEFT)

        clear_log_btn = ttk.Button(log_control_frame, text="🗑️ 清除日志", command=self.clear_log, width=12)
        clear_log_btn.pack(side=tk.RIGHT)

        # 日志文本框
        self.log_text = scrolledtext.ScrolledText(parent, height=8, width=80,
                                                 font=("Consolas", 9), wrap=tk.WORD)
        self.log_text.pack(fill=tk.BOTH, expand=True)

        # 设置日志文本框样式
        self.log_text.configure(bg='#f8f8f8', fg='#333333')
        
    def log_message(self, message):
        """添加日志消息"""
        timestamp = time.strftime("%Y-%m-%d %H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"

        # 如果log_text还没有创建，就先打印到控制台
        if hasattr(self, 'log_text') and self.log_text:
            self.log_text.insert(tk.END, log_entry)
            self.log_text.see(tk.END)

        print(message)  # 同时输出到控制台
        
    def clear_log(self):
        """清除日志"""
        self.log_text.delete(1.0, tk.END)

    def on_session_time_change(self, value):
        """会话时间滑块变化回调"""
        session_time = int(float(value))
        self.session_time = session_time
        self.session_label.config(text=f"{session_time}分钟")

    def save_settings(self):
        """保存设置到文件"""
        try:
            settings = {
                "username": self.username_var.get(),
                "password": self.password_var.get(),
                "proxy_host": self.proxy_host_var.get(),
                "proxy_port": self.proxy_port_var.get(),
                "bit_api_url": self.bit_api_var.get(),
                "session_time": self.session_time,
                "browser_id": self.browser_id_var.get(),  # 保存浏览器ID
                "browser_name": self.browser_name_var.get(),  # 保存浏览器名称
                "auto_change_enabled": self.auto_change_var.get(),  # 自动更换IP开关
                "auto_change_interval": self.interval_var.get()  # 自动更换IP间隔
            }

            with open(self.settings_file, 'w', encoding='utf-8') as f:
                json.dump(settings, f, indent=2, ensure_ascii=False)

            self.log_message("✅ 设置已保存")
            messagebox.showinfo("成功", "设置已保存到文件")

        except Exception as e:
            error_msg = f"保存设置失败: {e}"
            self.log_message(f"❌ {error_msg}")
            messagebox.showerror("错误", error_msg)

    def load_settings(self):
        """从文件加载设置"""
        try:
            if not hasattr(self, 'settings_file'):
                return

            import os
            if not os.path.exists(self.settings_file):
                self.log_message("ℹ️ 设置文件不存在，使用默认设置")
                return

            with open(self.settings_file, 'r', encoding='utf-8') as f:
                settings = json.load(f)

            # 更新界面控件
            if hasattr(self, 'username_var'):
                self.username_var.set(settings.get("username", "z123456_bYIud"))
            if hasattr(self, 'password_var'):
                self.password_var.set(settings.get("password", "Zzr442859970~"))
            if hasattr(self, 'proxy_host_var'):
                self.proxy_host_var.set(settings.get("proxy_host", "pr.oxylabs.io"))
            if hasattr(self, 'proxy_port_var'):
                self.proxy_port_var.set(settings.get("proxy_port", "7777"))
            if hasattr(self, 'bit_api_var'):
                self.bit_api_var.set(settings.get("bit_api_url", "http://127.0.0.1:54345"))

            # 更新会话时间
            self.session_time = settings.get("session_time", 10)
            if hasattr(self, 'session_time_var'):
                self.session_time_var.set(self.session_time)
            if hasattr(self, 'session_label'):
                self.session_label.config(text=f"{self.session_time}分钟")

            # 加载浏览器ID和名称
            if hasattr(self, 'browser_id_var'):
                browser_id = settings.get("browser_id", "")
                self.browser_id_var.set(browser_id)
                if browser_id:
                    self.current_browser_id = browser_id
                    # 如果有保存的浏览器ID，启用操作按钮
                    if hasattr(self, 'open_btn'):
                        self.open_btn.config(state=tk.NORMAL)
                    if hasattr(self, 'close_btn'):
                        self.close_btn.config(state=tk.NORMAL)

            if hasattr(self, 'browser_name_var'):
                self.browser_name_var.set(settings.get("browser_name", ""))

            # 加载自动更换IP设置
            if hasattr(self, 'auto_change_var'):
                self.auto_change_var.set(settings.get("auto_change_enabled", False))
            if hasattr(self, 'interval_var'):
                self.interval_var.set(settings.get("auto_change_interval", 30))

            self.log_message("✅ 设置已加载")

        except Exception as e:
            error_msg = f"加载设置失败: {e}"
            self.log_message(f"❌ {error_msg}")
            # 不显示错误对话框，因为这可能在初始化时调用
        
    def on_search_change(self, *args):
        """搜索框内容变化时的回调"""
        search_term = self.search_var.get().lower()
        if search_term:
            self.filtered_cities = [city for city in self.all_cities 
                                  if search_term in city.lower()]
        else:
            self.filtered_cities = self.all_cities.copy()
        self.update_city_list()
        
    def clear_search(self):
        """清除搜索框"""
        self.search_var.set("")
        
    def update_city_list(self):
        """更新城市列表显示"""
        self.city_listbox.delete(0, tk.END)
        for city in sorted(self.filtered_cities):
            self.city_listbox.insert(tk.END, city)
            
    def on_city_select(self, event):
        """城市选择回调"""
        selection = self.city_listbox.curselection()
        if selection:
            self.selected_city = self.city_listbox.get(selection[0])
            # 更新状态显示
            self.selected_city_label.config(text=f"✅ {self.selected_city}", foreground="green")
            self.log_message(f"🎯 选择城市: {self.selected_city}")

            # 启用创建按钮
            self.create_btn.config(state=tk.NORMAL)
            
    def generate_city_proxy_param(self, city_display_name):
        """生成城市代理参数"""
        # 从映射中获取英文城市名（已经是API格式）
        if city_display_name in self.city_mapping:
            city_en = self.city_mapping[city_display_name]
            # 直接使用验证过的API格式
            return f"cc-US-city-{city_en}"
        else:
            # 备用方法：从显示名称提取
            city_only = city_display_name.split(", ")[0]
            city_param = city_only.lower().replace(" ", "_")
            city_param = re.sub(r'[^a-z0-9_]', '', city_param)
            return f"cc-US-city-{city_param}"
        
    def detect_bit_browser_port(self):
        """检测比特浏览器API端口"""
        self.log_message("🔍 正在检测比特浏览器API端口...")

        for port in self.common_ports:
            try:
                test_url = f"http://127.0.0.1:{port}"
                # 使用POST方法和正确的参数测试
                test_data = {"page": 1, "pageSize": 10}
                response = requests.post(
                    f"{test_url}/browser/list",
                    json=test_data,
                    headers=self.bit_headers,
                    timeout=3
                )
                if response.status_code == 200:
                    result = response.json()
                    if result.get('success') is not False:  # 成功或者有数据
                        self.log_message(f"✅ 发现比特浏览器API端口: {port}")
                        self.bit_api_url = test_url
                        self.bit_api_var.set(test_url)
                        return True
            except:
                continue

        self.log_message("❌ 未找到可用的比特浏览器API端口")
        return False

    def test_bit_connection(self):
        """测试比特浏览器API连接"""
        try:
            self.bit_api_url = self.bit_api_var.get()
            # 使用POST方法和正确的参数测试
            test_data = {"page": 1, "pageSize": 10}
            response = requests.post(
                f"{self.bit_api_url}/browser/list",
                json=test_data,
                headers=self.bit_headers,
                timeout=10
            )

            if response.status_code == 200:
                result = response.json()
                # 检查响应是否有效
                if 'data' in result or result.get('success') is not False:
                    self.log_message("✅ 比特浏览器API连接成功")
                    browser_count = len(result.get('data', [])) if 'data' in result else 0
                    messagebox.showinfo("成功", f"比特浏览器API连接成功\n找到 {browser_count} 个浏览器窗口")
                    return True
                else:
                    error_msg = result.get('msg', '未知错误')
                    self.log_message(f"❌ API响应错误: {error_msg}")
                    messagebox.showerror("错误", f"API响应错误: {error_msg}")
                    return False
            else:
                self.log_message(f"❌ 比特浏览器API连接失败: HTTP {response.status_code}")

                # 如果失败，尝试自动检测端口
                if messagebox.askyesno("检测端口", "API连接失败，是否自动检测比特浏览器端口？"):
                    return self.detect_bit_browser_port()
                else:
                    messagebox.showerror("错误", f"API连接失败: HTTP {response.status_code}")
                    return False
        except Exception as e:
            self.log_message(f"❌ 比特浏览器API连接失败: {e}")

            # 如果失败，尝试自动检测端口
            if messagebox.askyesno("检测端口", "API连接失败，是否自动检测比特浏览器端口？"):
                return self.detect_bit_browser_port()
            else:
                messagebox.showerror("错误", f"API连接失败: {e}")
                return False
            
    def create_or_update_browser(self):
        """创建或更新浏览器窗口"""
        if not self.selected_city:
            messagebox.showwarning("警告", "请先选择一个城市")
            return
            
        if not self.username_var.get() or not self.password_var.get():
            messagebox.showwarning("警告", "请先配置Oxylabs用户名和密码")
            return
            
        def create_browser_thread():
            try:
                # 生成代理参数（包含会话时间）
                city_param = self.generate_city_proxy_param(self.selected_city)
                import random
                session_id = f"sess{random.randint(10000, 99999)}"
                proxy_username = f"customer-{self.username_var.get()}-{city_param}-sessid-{session_id}-sesstime-{self.session_time}"
                
                self.log_message(f"开始创建/更新浏览器窗口...")
                self.log_message(f"使用代理: {proxy_username}")
                
                # 准备API数据（使用最基本的browserFingerPrint配置）
                json_data = {
                    'name': self.browser_name_var.get(),
                    'remark': f'US City: {self.selected_city}',
                    'proxyMethod': 2,  # 自定义代理
                    'proxyType': 'http',
                    'host': self.proxy_host_var.get(),
                    'port': self.proxy_port_var.get(),
                    'proxyUserName': proxy_username,
                    'proxyPassword': self.password_var.get(),
                    'browserFingerPrint': {}  # 空的指纹配置，让BitBrowser使用默认值
                }
                
                # 如果有指定ID，则更新；否则创建新的
                browser_id = self.browser_id_var.get().strip()
                if browser_id:
                    json_data['id'] = browser_id
                
                # 发送API请求
                response = requests.post(
                    f"{self.bit_api_url}/browser/update",
                    data=json.dumps(json_data),
                    headers=self.bit_headers,
                    timeout=60  # 增加超时时间
                )

                if response.status_code == 200:
                    result = response.json()
                    if result.get('success'):
                        self.current_browser_id = result['data']['id']
                        self.browser_id_var.set(self.current_browser_id)
                        self.log_message(f"✅ 浏览器窗口创建/更新成功: {self.current_browser_id}")

                        # 保存浏览器ID到设置
                        self.save_settings()

                        # 启用打开按钮
                        self.root.after(0, lambda: self.open_btn.config(state=tk.NORMAL))
                        success_msg = f"浏览器窗口创建成功\nID: {self.current_browser_id}\n设置已自动保存"
                        self.root.after(0, lambda msg=success_msg: messagebox.showinfo("成功", msg))
                    else:
                        error_msg = result.get('msg', '未知错误')
                        self.log_message(f"❌ 创建浏览器窗口失败: {error_msg}")
                        self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"创建失败: {msg}"))
                else:
                    error_msg = f"HTTP {response.status_code}"
                    self.log_message(f"❌ API请求失败: {error_msg}")
                    self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"API请求失败: {msg}"))

            except Exception as e:
                error_msg = str(e)
                self.log_message(f"❌ 创建浏览器窗口时出错: {error_msg}")
                self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"创建失败: {msg}"))
        
        # 在后台线程中执行
        Thread(target=create_browser_thread, daemon=True).start()
        
    def open_browser(self):
        """打开浏览器窗口"""
        browser_id = self.browser_id_var.get().strip()
        if not browser_id:
            messagebox.showwarning("警告", "请先创建浏览器窗口")
            return
            
        def open_browser_thread():
            try:
                self.log_message(f"正在打开浏览器窗口: {browser_id}")

                # 增加重试机制
                max_retries = 3
                for attempt in range(max_retries):
                    try:
                        json_data = {"id": browser_id}
                        response = requests.post(
                            f"{self.bit_api_url}/browser/open",
                            data=json.dumps(json_data),
                            headers=self.bit_headers,
                            timeout=60  # 增加超时时间
                        )

                        if response.status_code == 200:
                            result = response.json()
                            if result.get('success'):
                                self.log_message(f"✅ 浏览器窗口打开成功")
                                self.log_message(f"调试地址: {result['data'].get('http', 'N/A')}")
                                self.log_message(f"WebSocket地址: {result['data'].get('ws', 'N/A')}")

                                # 启用关闭按钮
                                self.root.after(0, lambda: self.close_btn.config(state=tk.NORMAL))
                                self.root.after(0, lambda: messagebox.showinfo("成功", "浏览器窗口已打开"))
                                return
                            else:
                                error_msg = result.get('msg', '未知错误')
                                self.log_message(f"❌ 打开浏览器窗口失败: {error_msg}")

                                # 如果是"浏览器正在打开中"错误，等待后重试
                                if "正在打开中" in error_msg or "opening" in error_msg.lower():
                                    if attempt < max_retries - 1:
                                        self.log_message(f"浏览器正在打开中，等待5秒后重试... ({attempt + 1}/{max_retries})")
                                        time.sleep(5)
                                        continue

                                self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"打开失败: {msg}"))
                                return
                        else:
                            error_msg = f"HTTP {response.status_code}"
                            self.log_message(f"❌ API请求失败: {error_msg}")
                            if attempt == max_retries - 1:  # 最后一次尝试
                                self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"API请求失败: {msg}"))
                            else:
                                self.log_message(f"正在重试... ({attempt + 1}/{max_retries})")
                                time.sleep(2)
                                continue

                    except requests.exceptions.Timeout:
                        if attempt == max_retries - 1:  # 最后一次尝试
                            error_msg = "连接超时，请检查比特浏览器是否正常运行"
                            self.log_message(f"❌ {error_msg}")
                            self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", msg))
                        else:
                            self.log_message(f"连接超时，正在重试... ({attempt + 1}/{max_retries})")
                            time.sleep(3)
                            continue

            except Exception as e:
                error_msg = str(e)
                self.log_message(f"❌ 打开浏览器窗口时出错: {error_msg}")
                self.root.after(0, lambda msg=error_msg: messagebox.showerror("错误", f"打开失败: {msg}"))
        
        # 在后台线程中执行
        Thread(target=open_browser_thread, daemon=True).start()
        
    def close_browser(self):
        """关闭浏览器窗口"""
        browser_id = self.browser_id_var.get().strip()
        if not browser_id:
            return
            
        def close_browser_thread():
            try:
                self.log_message(f"正在关闭浏览器窗口: {browser_id}")

                json_data = {"id": browser_id}
                response = requests.post(
                    f"{self.bit_api_url}/browser/close",
                    data=json.dumps(json_data),
                    headers=self.bit_headers,
                    timeout=30
                )

                if response.status_code == 200:
                    self.log_message(f"✅ 浏览器窗口关闭成功")
                    # 禁用关闭按钮
                    self.root.after(0, lambda: self.close_btn.config(state=tk.DISABLED))
                else:
                    error_msg = f"HTTP {response.status_code}"
                    self.log_message(f"❌ 关闭浏览器窗口失败: {error_msg}")

            except Exception as e:
                error_msg = str(e)
                self.log_message(f"❌ 关闭浏览器窗口时出错: {error_msg}")
        
        # 在后台线程中执行
        Thread(target=close_browser_thread, daemon=True).start()

    # ==================== 自动更换IP功能 ====================

    def toggle_auto_change(self):
        """切换自动更换IP开关"""
        self.auto_change_enabled = self.auto_change_var.get()
        if self.auto_change_enabled:
            self.log_message("🔄 自动更换IP已启用")
        else:
            self.log_message("⏹️ 自动更换IP已禁用")
            self.stop_auto_change()

    def start_auto_change(self):
        """开始自动更换IP"""
        if not self.selected_city:
            messagebox.showwarning("警告", "请先选择一个城市！")
            return

        if not self.browser_id_var.get():
            messagebox.showwarning("警告", "请先输入浏览器ID！")
            return

        # 更新间隔时间
        self.auto_change_interval = self.interval_var.get()
        self.remaining_time = self.auto_change_interval * 60  # 转换为秒

        # 更新UI状态
        self.auto_status_label.config(text="✅ 运行中", foreground="green")
        self.start_auto_btn.config(state=tk.DISABLED)
        self.stop_auto_btn.config(state=tk.NORMAL)

        # 启动定时器
        self.schedule_next_change()
        self.start_countdown()

        self.log_message(f"🚀 自动更换IP已启动，间隔: {self.auto_change_interval}分钟")

    def stop_auto_change(self):
        """停止自动更换IP"""
        # 取消定时器
        if self.auto_change_timer:
            self.auto_change_timer.cancel()
            self.auto_change_timer = None

        if self.countdown_timer:
            self.countdown_timer.cancel()
            self.countdown_timer = None

        # 更新UI状态
        self.auto_status_label.config(text="❌ 已停止", foreground="red")
        self.countdown_label.config(text="--:--")
        self.start_auto_btn.config(state=tk.NORMAL)
        self.stop_auto_btn.config(state=tk.DISABLED)

        self.log_message("⏹️ 自动更换IP已停止")

    def schedule_next_change(self):
        """安排下次IP更换"""
        if self.auto_change_timer:
            self.auto_change_timer.cancel()

        # 创建定时器
        self.auto_change_timer = Timer(self.auto_change_interval * 60, self.execute_auto_change)
        self.auto_change_timer.start()

    def start_countdown(self):
        """开始倒计时显示"""
        if self.remaining_time > 0:
            minutes = self.remaining_time // 60
            seconds = self.remaining_time % 60
            self.countdown_label.config(text=f"{minutes:02d}:{seconds:02d}")

            self.remaining_time -= 1

            # 安排下次更新
            self.countdown_timer = Timer(1.0, self.start_countdown)
            self.countdown_timer.start()
        else:
            self.countdown_label.config(text="更换中...")

    def execute_auto_change(self):
        """执行自动更换IP"""
        try:
            self.log_message(f"🔄 开始自动更换IP - 城市: {self.selected_city}")

            # 先关闭浏览器
            self.close_browser_silent()

            # 等待一秒
            time.sleep(1)

            # 更新浏览器配置（使用相同城市）
            success = self.update_browser_proxy()

            if success:
                # 显示成功弹窗
                self.show_success_popup()

                # 重置倒计时
                self.remaining_time = self.auto_change_interval * 60

                # 安排下次更换
                self.schedule_next_change()
                self.start_countdown()

                self.log_message(f"✅ IP更换成功！下次更换时间: {self.auto_change_interval}分钟后")
            else:
                self.log_message("❌ IP更换失败，自动更换已停止")
                self.stop_auto_change()

        except Exception as e:
            self.log_message(f"❌ 自动更换IP出错: {str(e)}")
            self.stop_auto_change()

    def close_browser_silent(self):
        """静默关闭浏览器（不显示错误消息）"""
        try:
            browser_id = self.browser_id_var.get()
            if not browser_id:
                return

            close_url = f"{self.bit_api_url}/browser/close"
            close_data = {"id": browser_id}

            response = requests.post(close_url, json=close_data, headers=self.bit_headers, timeout=30)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    self.log_message(f"🔒 浏览器已关闭 (ID: {browser_id})")
                    return True

        except Exception:
            pass  # 静默处理错误

        return False

    def update_browser_proxy(self):
        """更新浏览器代理配置"""
        try:
            browser_id = self.browser_id_var.get()
            if not browser_id or not self.selected_city:
                return False

            # 生成新的会话ID和代理用户名
            session_id = f"session_{int(time.time())}"
            city_param = self.generate_city_proxy_param(self.selected_city)
            proxy_username = f"customer-{self.username_var.get()}-cc-US-city-{city_param}-sessid-{session_id}-sesstime-{self.session_time}"

            # 准备更新数据
            update_data = {
                'id': browser_id,
                'name': self.browser_name_var.get(),
                'remark': f'Auto IP Change - {self.selected_city}',
                'proxyMethod': 2,
                'proxyType': 'http',
                'host': self.proxy_host_var.get(),
                'port': self.proxy_port_var.get(),
                'proxyUserName': proxy_username,
                'proxyPassword': self.password_var.get(),
                'browserFingerPrint': {}
            }

            # 发送更新请求
            update_url = f"{self.bit_api_url}/browser/update"
            response = requests.post(update_url, json=update_data, headers=self.bit_headers, timeout=60)

            if response.status_code == 200:
                result = response.json()
                if result.get('success'):
                    return True
                else:
                    self.log_message(f"❌ 更新失败: {result.get('msg', '未知错误')}")
            else:
                self.log_message(f"❌ 更新请求失败: HTTP {response.status_code}")

        except Exception as e:
            self.log_message(f"❌ 更新浏览器配置出错: {str(e)}")

        return False

    def show_success_popup(self):
        """显示更换成功弹窗"""
        def close_popup():
            popup.destroy()

        # 创建弹窗
        popup = tk.Toplevel(self.root)
        popup.title("更换成功")
        popup.geometry("300x150")
        popup.resizable(False, False)
        popup.configure(bg=BitBrowserColors.CARD_BG)

        # 居中显示
        popup.transient(self.root)
        popup.grab_set()

        # 计算居中位置
        popup.update_idletasks()
        x = (popup.winfo_screenwidth() // 2) - (300 // 2)
        y = (popup.winfo_screenheight() // 2) - (150 // 2)
        popup.geometry(f"300x150+{x}+{y}")

        # 成功图标和消息
        icon_label = tk.Label(popup, text="✅", font=("Arial", 32),
                             bg=BitBrowserColors.CARD_BG, fg=BitBrowserColors.SUCCESS)
        icon_label.pack(pady=(20, 10))

        msg_label = tk.Label(popup, text="IP更换成功！", font=("Microsoft YaHei", 12, "bold"),
                           bg=BitBrowserColors.CARD_BG, fg=BitBrowserColors.TEXT_PRIMARY)
        msg_label.pack()

        city_label = tk.Label(popup, text=f"城市: {self.selected_city}", font=("Microsoft YaHei", 10),
                            bg=BitBrowserColors.CARD_BG, fg=BitBrowserColors.TEXT_SECONDARY)
        city_label.pack(pady=(5, 15))

        # 确定按钮
        ok_btn = tk.Button(popup, text="确定", command=close_popup,
                          bg=BitBrowserColors.PRIMARY, fg="white",
                          font=("Microsoft YaHei", 10), width=10, height=1,
                          relief="flat", cursor="hand2")
        ok_btn.pack()

        # 3秒后自动关闭
        popup.after(3000, close_popup)


def main():
    """主函数"""
    root = tk.Tk()
    app = BitBrowserUSCityTool(root)
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
