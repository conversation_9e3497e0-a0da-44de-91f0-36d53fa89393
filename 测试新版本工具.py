#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试新版本工具功能
"""

import tkinter as tk
from tkinter import ttk
import sys
import os

# 添加当前目录到路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_new_version():
    """测试新版本功能"""
    try:
        # 导入新版本
        from 比特浏览器美国城市代理工具_v2 import BitBrowserUSCityTool, ConfigWindow, BitBrowserColors
        
        print("✅ 成功导入新版本模块")
        print(f"✅ 颜色配置: {BitBrowserColors.PRIMARY}")
        
        # 创建测试窗口
        root = tk.Tk()
        root.title("测试 - 比特浏览器美国城市代理工具 v2.0")
        root.geometry("1000x700")
        
        # 创建应用实例
        app = BitBrowserUSCityTool(root)
        print("✅ 成功创建应用实例")
        
        # 检查新功能
        features_to_check = [
            ('config', '配置管理'),
            ('open_config_window', '配置窗口'),
            ('start_city_test', '城市测试'),
            ('update_verified_cities', '更新城市列表'),
            ('start_auto_change', '自动更换IP'),
            ('test_city_ip', '单城市测试'),
            ('show_test_results', '测试结果显示'),
            ('auto_change_enabled', '自动更换状态'),
            ('test_in_progress', '测试进行状态'),
            ('test_results', '测试结果存储')
        ]
        
        for attr, desc in features_to_check:
            if hasattr(app, attr):
                print(f"✅ {desc}: 已添加")
            else:
                print(f"❌ {desc}: 未找到")
        
        # 检查界面组件
        ui_components = [
            ('test_cities_btn', '城市测试按钮'),
            ('update_cities_btn', '更新城市按钮'),
            ('test_progress_var', '测试进度变量'),
            ('test_progress_label', '测试进度标签'),
            ('start_auto_btn', '开始自动更换按钮'),
            ('stop_auto_btn', '停止自动更换按钮'),
            ('auto_status_label', '自动更换状态标签'),
            ('countdown_label', '倒计时标签')
        ]
        
        for attr, desc in ui_components:
            if hasattr(app, attr):
                print(f"✅ {desc}: 已创建")
            else:
                print(f"❌ {desc}: 未创建")
        
        print("\n🎯 新版本功能检查完成，启动界面...")
        
        # 居中显示窗口
        root.update_idletasks()
        width = root.winfo_width()
        height = root.winfo_height()
        x = (root.winfo_screenwidth() // 2) - (width // 2)
        y = (root.winfo_screenheight() // 2) - (height // 2)
        root.geometry(f"{width}x{height}+{x}+{y}")
        
        # 启动主循环
        root.mainloop()
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🚀 开始测试新版本工具...")
    test_new_version()
