#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
美国城市选择器 - Oxylabs代理城市参数生成工具

这个工具提供一个图形界面，让用户可以选择美国的城市，
并生成对应的Oxylabs代理参数格式。

功能：
- 搜索美国城市
- 选择城市
- 生成Oxylabs代理参数格式
- 复制到剪贴板

使用方法：
1. 运行脚本
2. 在搜索框中输入城市名称
3. 从列表中选择城市
4. 点击"生成代理参数"按钮
5. 复制生成的参数

作者: AI助手
日期: 2025-01-02
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import pyperclip
import re

class USCitySelector:
    def __init__(self, root):
        self.root = root
        self.root.title("美国城市选择器 - Oxylabs代理参数生成工具")
        self.root.geometry("800x600")
        self.root.resizable(True, True)
        
        # 美国主要城市列表（按州分组）
        self.us_cities = {
            "Alabama": ["Birmingham", "Montgomery", "Mobile", "Huntsville"],
            "Alaska": ["Anchorage", "Fairbanks", "Juneau"],
            "Arizona": ["Phoenix", "Tucson", "Mesa", "Chandler", "Scottsdale"],
            "Arkansas": ["Little Rock", "Fort Smith", "Fayetteville"],
            "California": ["Los Angeles", "San Francisco", "San Diego", "San Jose", "Sacramento", 
                          "Oakland", "Santa Ana", "Anaheim", "Riverside", "Stockton", "Bakersfield",
                          "Fremont", "San Bernardino", "Modesto", "Oxnard", "Fontana", "Moreno Valley"],
            "Colorado": ["Denver", "Colorado Springs", "Aurora", "Fort Collins", "Lakewood"],
            "Connecticut": ["Bridgeport", "New Haven", "Hartford", "Stamford", "Waterbury"],
            "Delaware": ["Wilmington", "Dover", "Newark"],
            "Florida": ["Jacksonville", "Miami", "Tampa", "Orlando", "St. Petersburg", "Hialeah",
                       "Tallahassee", "Fort Lauderdale", "Port St. Lucie", "Cape Coral"],
            "Georgia": ["Atlanta", "Augusta", "Columbus", "Savannah", "Athens"],
            "Hawaii": ["Honolulu", "Pearl City", "Hilo"],
            "Idaho": ["Boise", "Nampa", "Meridian"],
            "Illinois": ["Chicago", "Aurora", "Rockford", "Joliet", "Naperville", "Springfield",
                        "Peoria", "Elgin", "Waukegan"],
            "Indiana": ["Indianapolis", "Fort Wayne", "Evansville", "South Bend", "Carmel"],
            "Iowa": ["Des Moines", "Cedar Rapids", "Davenport", "Sioux City"],
            "Kansas": ["Wichita", "Overland Park", "Kansas City", "Topeka"],
            "Kentucky": ["Louisville", "Lexington", "Bowling Green", "Owensboro"],
            "Louisiana": ["New Orleans", "Baton Rouge", "Shreveport", "Lafayette"],
            "Maine": ["Portland", "Lewiston", "Bangor"],
            "Maryland": ["Baltimore", "Frederick", "Rockville", "Gaithersburg", "Bowie"],
            "Massachusetts": ["Boston", "Worcester", "Springfield", "Lowell", "Cambridge"],
            "Michigan": ["Detroit", "Grand Rapids", "Warren", "Sterling Heights", "Lansing"],
            "Minnesota": ["Minneapolis", "St. Paul", "Rochester", "Duluth", "Bloomington"],
            "Mississippi": ["Jackson", "Gulfport", "Southaven", "Hattiesburg"],
            "Missouri": ["Kansas City", "St. Louis", "Springfield", "Independence", "Columbia"],
            "Montana": ["Billings", "Missoula", "Great Falls", "Bozeman"],
            "Nebraska": ["Omaha", "Lincoln", "Bellevue", "Grand Island"],
            "Nevada": ["Las Vegas", "Henderson", "Reno", "North Las Vegas", "Sparks"],
            "New Hampshire": ["Manchester", "Nashua", "Concord", "Dover"],
            "New Jersey": ["Newark", "Jersey City", "Paterson", "Elizabeth", "Edison"],
            "New Mexico": ["Albuquerque", "Las Cruces", "Rio Rancho", "Santa Fe"],
            "New York": ["New York", "Buffalo", "Rochester", "Yonkers", "Syracuse", "Albany",
                        "New Rochelle", "Mount Vernon", "Schenectady", "Utica"],
            "North Carolina": ["Charlotte", "Raleigh", "Greensboro", "Durham", "Winston-Salem",
                              "Fayetteville", "Cary", "Wilmington", "High Point"],
            "North Dakota": ["Fargo", "Bismarck", "Grand Forks", "Minot"],
            "Ohio": ["Columbus", "Cleveland", "Cincinnati", "Toledo", "Akron", "Dayton"],
            "Oklahoma": ["Oklahoma City", "Tulsa", "Norman", "Broken Arrow", "Lawton"],
            "Oregon": ["Portland", "Eugene", "Salem", "Gresham", "Hillsboro"],
            "Pennsylvania": ["Philadelphia", "Pittsburgh", "Allentown", "Erie", "Reading"],
            "Rhode Island": ["Providence", "Warwick", "Cranston", "Pawtucket"],
            "South Carolina": ["Columbia", "Charleston", "North Charleston", "Mount Pleasant"],
            "South Dakota": ["Sioux Falls", "Rapid City", "Aberdeen", "Brookings"],
            "Tennessee": ["Nashville", "Memphis", "Knoxville", "Chattanooga", "Clarksville"],
            "Texas": ["Houston", "San Antonio", "Dallas", "Austin", "Fort Worth", "El Paso",
                     "Arlington", "Corpus Christi", "Plano", "Laredo", "Lubbock", "Garland"],
            "Utah": ["Salt Lake City", "West Valley City", "Provo", "West Jordan", "Orem"],
            "Vermont": ["Burlington", "South Burlington", "Rutland", "Barre"],
            "Virginia": ["Virginia Beach", "Norfolk", "Chesapeake", "Richmond", "Newport News"],
            "Washington": ["Seattle", "Spokane", "Tacoma", "Vancouver", "Bellevue"],
            "West Virginia": ["Charleston", "Huntington", "Parkersburg", "Morgantown"],
            "Wisconsin": ["Milwaukee", "Madison", "Green Bay", "Kenosha", "Racine"],
            "Wyoming": ["Cheyenne", "Casper", "Laramie", "Gillette"]
        }
        
        # 创建所有城市的平面列表
        self.all_cities = []
        for state, cities in self.us_cities.items():
            for city in cities:
                self.all_cities.append(f"{city}, {state}")
        
        self.filtered_cities = self.all_cities.copy()
        self.selected_city = None
        
        self.create_widgets()
        
    def create_widgets(self):
        # 主框架
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 配置网格权重
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # 标题
        title_label = ttk.Label(main_frame, text="美国城市选择器", 
                               font=("Arial", 16, "bold"))
        title_label.grid(row=0, column=0, columnspan=3, pady=(0, 10))
        
        # 搜索框
        ttk.Label(main_frame, text="搜索城市:").grid(row=1, column=0, sticky=tk.W, padx=(0, 5))
        self.search_var = tk.StringVar()
        self.search_var.trace('w', self.on_search_change)
        search_entry = ttk.Entry(main_frame, textvariable=self.search_var, width=30)
        search_entry.grid(row=1, column=1, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 清除搜索按钮
        clear_btn = ttk.Button(main_frame, text="清除", command=self.clear_search)
        clear_btn.grid(row=1, column=2, padx=(5, 0))
        
        # 城市列表框架
        list_frame = ttk.LabelFrame(main_frame, text="选择城市", padding="5")
        list_frame.grid(row=2, column=0, columnspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        list_frame.columnconfigure(0, weight=1)
        list_frame.rowconfigure(0, weight=1)
        
        # 城市列表
        self.city_listbox = tk.Listbox(list_frame, height=15)
        self.city_listbox.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        self.city_listbox.bind('<<ListboxSelect>>', self.on_city_select)
        
        # 滚动条
        scrollbar = ttk.Scrollbar(list_frame, orient=tk.VERTICAL, command=self.city_listbox.yview)
        scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        self.city_listbox.configure(yscrollcommand=scrollbar.set)
        
        # 结果框架
        result_frame = ttk.LabelFrame(main_frame, text="生成的代理参数", padding="5")
        result_frame.grid(row=3, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        result_frame.columnconfigure(0, weight=1)
        
        # 选中城市显示
        self.selected_label = ttk.Label(result_frame, text="未选择城市", 
                                       font=("Arial", 10, "bold"))
        self.selected_label.grid(row=0, column=0, columnspan=2, sticky=tk.W, pady=(0, 5))
        
        # 代理参数显示
        self.proxy_var = tk.StringVar()
        proxy_entry = ttk.Entry(result_frame, textvariable=self.proxy_var, 
                               state="readonly", width=50)
        proxy_entry.grid(row=1, column=0, sticky=(tk.W, tk.E), padx=(0, 5))
        
        # 复制按钮
        copy_btn = ttk.Button(result_frame, text="复制", command=self.copy_to_clipboard)
        copy_btn.grid(row=1, column=1)
        
        # 说明文本
        info_text = """
使用说明：
1. 在搜索框中输入城市名称进行搜索
2. 从列表中选择您需要的城市
3. 生成的代理参数格式为：cc-US-city-城市名
4. 点击"复制"按钮将参数复制到剪贴板

示例用法：
curl -x pr.oxylabs.io:7777 -U "customer-USERNAME-cc-US-city-new_york:PASSWORD" https://ip.oxylabs.io/location
        """
        
        info_frame = ttk.LabelFrame(main_frame, text="使用说明", padding="5")
        info_frame.grid(row=4, column=0, columnspan=3, sticky=(tk.W, tk.E), pady=(10, 0))
        info_frame.columnconfigure(0, weight=1)
        
        info_label = ttk.Label(info_frame, text=info_text.strip(), 
                              justify=tk.LEFT, wraplength=750)
        info_label.grid(row=0, column=0, sticky=(tk.W, tk.E))
        
        # 初始化城市列表
        self.update_city_list()
        
    def on_search_change(self, *args):
        """搜索框内容变化时的回调"""
        search_term = self.search_var.get().lower()
        if search_term:
            self.filtered_cities = [city for city in self.all_cities 
                                  if search_term in city.lower()]
        else:
            self.filtered_cities = self.all_cities.copy()
        self.update_city_list()
        
    def clear_search(self):
        """清除搜索框"""
        self.search_var.set("")
        
    def update_city_list(self):
        """更新城市列表显示"""
        self.city_listbox.delete(0, tk.END)
        for city in sorted(self.filtered_cities):
            self.city_listbox.insert(tk.END, city)
            
    def on_city_select(self, event):
        """城市选择回调"""
        selection = self.city_listbox.curselection()
        if selection:
            self.selected_city = self.city_listbox.get(selection[0])
            self.update_proxy_parameter()
            
    def update_proxy_parameter(self):
        """更新代理参数显示"""
        if self.selected_city:
            # 提取城市名称（去掉州名）
            city_name = self.selected_city.split(", ")[0]
            # 转换为Oxylabs格式（小写，空格替换为下划线）
            city_param = city_name.lower().replace(" ", "_")
            # 移除特殊字符
            city_param = re.sub(r'[^a-z0-9_]', '', city_param)
            
            proxy_param = f"cc-US-city-{city_param}"
            self.proxy_var.set(proxy_param)
            self.selected_label.config(text=f"已选择: {self.selected_city}")
        else:
            self.proxy_var.set("")
            self.selected_label.config(text="未选择城市")
            
    def copy_to_clipboard(self):
        """复制到剪贴板"""
        proxy_param = self.proxy_var.get()
        if proxy_param:
            try:
                pyperclip.copy(proxy_param)
                messagebox.showinfo("成功", f"已复制到剪贴板:\n{proxy_param}")
            except Exception as e:
                messagebox.showerror("错误", f"复制失败: {e}")
        else:
            messagebox.showwarning("警告", "请先选择一个城市")

def main():
    """主函数"""
    try:
        import pyperclip
    except ImportError:
        print("警告: 未安装pyperclip库，复制功能将不可用")
        print("请运行: pip install pyperclip")
        
    root = tk.Tk()
    app = USCitySelector(root)
    
    # 设置窗口图标（如果有的话）
    try:
        root.iconbitmap("icon.ico")
    except:
        pass
    
    # 居中显示窗口
    root.update_idletasks()
    width = root.winfo_width()
    height = root.winfo_height()
    x = (root.winfo_screenwidth() // 2) - (width // 2)
    y = (root.winfo_screenheight() // 2) - (height // 2)
    root.geometry(f"{width}x{height}+{x}+{y}")
    
    root.mainloop()

if __name__ == "__main__":
    main()
