#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试BitBrowser API连接

检查BitBrowser是否正在运行，并测试API响应
"""

import requests
import json

def test_bitbrowser_connection():
    """测试BitBrowser连接"""
    print("=" * 60)
    print("测试BitBrowser API连接")
    print("=" * 60)
    
    # 测试不同的端口
    ports = [54345, 54346, 54347]
    headers = {'Content-Type': 'application/json'}
    
    for port in ports:
        url = f"http://127.0.0.1:{port}"
        print(f"\n测试端口: {port}")
        print("-" * 40)
        
        try:
            # 测试基本连接
            response = requests.get(f"{url}/", timeout=5)
            print(f"✅ 端口 {port} 可访问")
            print(f"状态码: {response.status_code}")
            print(f"响应内容: {response.text[:200]}...")
            
            # 测试浏览器列表API
            try:
                list_data = {"page": 1, "pageSize": 10}
                list_response = requests.post(
                    f"{url}/browser/list",
                    data=json.dumps(list_data),
                    headers=headers,
                    timeout=10
                )
                print(f"浏览器列表API状态码: {list_response.status_code}")
                if list_response.status_code == 200:
                    list_result = list_response.json()
                    print(f"浏览器列表响应: {json.dumps(list_result, indent=2, ensure_ascii=False)}")
                else:
                    print(f"浏览器列表API错误: {list_response.text}")
            except Exception as e:
                print(f"浏览器列表API测试失败: {e}")
            
            # 测试创建浏览器API
            try:
                create_data = {
                    'name': 'test_browser',
                    'remark': 'API测试',
                    'proxyMethod': 2,
                    'proxyType': 'noproxy',
                    'host': '',
                    'port': '',
                    'proxyUserName': '',
                    "browserFingerPrint": {
                        'coreVersion': '124'
                    }
                }
                create_response = requests.post(
                    f"{url}/browser/update",
                    data=json.dumps(create_data),
                    headers=headers,
                    timeout=10
                )
                print(f"创建浏览器API状态码: {create_response.status_code}")
                if create_response.status_code == 200:
                    create_result = create_response.json()
                    print(f"创建浏览器响应: {json.dumps(create_result, indent=2, ensure_ascii=False)}")
                    
                    # 如果创建成功，尝试删除
                    if create_result.get('success') and 'data' in create_result:
                        browser_id = create_result['data']['id']
                        delete_data = {'id': browser_id}
                        delete_response = requests.post(
                            f"{url}/browser/delete",
                            data=json.dumps(delete_data),
                            headers=headers,
                            timeout=10
                        )
                        print(f"删除测试浏览器: {delete_response.status_code}")
                else:
                    print(f"创建浏览器API错误: {create_response.text}")
            except Exception as e:
                print(f"创建浏览器API测试失败: {e}")
            
            return port  # 返回可用端口
            
        except requests.exceptions.ConnectionError:
            print(f"❌ 端口 {port} 无法连接")
        except requests.exceptions.Timeout:
            print(f"❌ 端口 {port} 连接超时")
        except Exception as e:
            print(f"❌ 端口 {port} 测试失败: {e}")
    
    print("\n❌ 所有端口都无法连接到BitBrowser")
    return None

def test_proxy_configuration():
    """测试代理配置"""
    print("\n" + "=" * 60)
    print("测试代理配置格式")
    print("=" * 60)
    
    # 测试Oxylabs代理参数
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    city_param = "cc-US-city-los_angeles"
    session_id = "sess12345"
    session_time = 15
    
    proxy_username = f"customer-{username}-{city_param}-sessid-{session_id}-sesstime-{session_time}"
    
    print(f"代理主机: pr.oxylabs.io")
    print(f"代理端口: 7777")
    print(f"代理用户名: {proxy_username}")
    print(f"代理密码: {password}")
    print(f"完整代理URL: http://{proxy_username}:{password}@pr.oxylabs.io:7777")
    
    # 测试代理配置JSON
    proxy_config = {
        'proxyMethod': 2,
        'proxyType': 'http',
        'host': 'pr.oxylabs.io',
        'port': '7777',
        'proxyUserName': proxy_username,
        'proxyPassword': password
    }
    
    print(f"\nBitBrowser代理配置:")
    print(json.dumps(proxy_config, indent=2, ensure_ascii=False))

def main():
    """主函数"""
    try:
        print("🔍 开始诊断BitBrowser连接问题")
        
        # 测试连接
        available_port = test_bitbrowser_connection()
        
        # 测试代理配置
        test_proxy_configuration()
        
        print("\n" + "=" * 60)
        print("诊断总结")
        print("=" * 60)
        
        if available_port:
            print(f"✅ BitBrowser在端口 {available_port} 上运行正常")
            print("建议:")
            print("1. 确保在工具中使用正确的端口")
            print("2. 检查代理配置是否正确")
            print("3. 确保Oxylabs账户有效")
        else:
            print("❌ BitBrowser未运行或无法访问")
            print("解决方案:")
            print("1. 启动BitBrowser应用程序")
            print("2. 检查BitBrowser是否开启了本地API服务")
            print("3. 确认防火墙没有阻止端口访问")
            print("4. 尝试重启BitBrowser")
        
    except Exception as e:
        print(f"\n❌ 诊断过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
