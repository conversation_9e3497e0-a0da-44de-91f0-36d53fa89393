#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试中文界面功能

验证城市名称的中英文映射和代理参数生成是否正确。

作者: AI助手
日期: 2025-01-02
"""

import re

def test_city_mapping():
    """测试城市映射功能"""
    print("=" * 60)
    print("测试中文城市界面功能")
    print("=" * 60)
    
    # 美国主要城市列表（中英文对照）- 测试版本
    us_cities = {
        "加利福尼亚州": [
            ("洛杉矶", "Los Angeles"), ("旧金山", "San Francisco"), ("圣地亚哥", "San Diego")
        ],
        "纽约州": [
            ("纽约", "New York"), ("布法罗", "Buffalo"), ("罗切斯特", "Rochester")
        ],
        "得克萨斯州": [
            ("休斯顿", "Houston"), ("圣安东尼奥", "San Antonio"), ("达拉斯", "Dallas")
        ],
        "佛罗里达州": [
            ("杰克逊维尔", "Jacksonville"), ("迈阿密", "Miami"), ("坦帕", "Tampa")
        ],
        "伊利诺伊州": [
            ("芝加哥", "Chicago"), ("奥罗拉", "Aurora"), ("罗克福德", "Rockford")
        ]
    }
    
    # 创建映射
    city_mapping = {}
    all_cities = []
    
    for state_cn, cities in us_cities.items():
        for city_cn, city_en in cities:
            display_name = f"{city_cn}, {state_cn}"
            all_cities.append(display_name)
            city_mapping[display_name] = city_en
    
    print("🏙️ 城市列表（中文显示）:")
    print("-" * 40)
    for i, city in enumerate(all_cities, 1):
        print(f"{i:2d}. {city}")
    
    print(f"\n📊 总计: {len(all_cities)} 个城市")
    
    return city_mapping, all_cities

def generate_city_proxy_param(city_display_name, city_mapping):
    """生成城市代理参数"""
    if city_display_name in city_mapping:
        city_en = city_mapping[city_display_name]
        # 转换为Oxylabs格式
        city_param = city_en.lower().replace(" ", "_")
        city_param = re.sub(r'[^a-z0-9_]', '', city_param)
        return f"cc-US-city-{city_param}"
    else:
        return None

def test_proxy_generation():
    """测试代理参数生成"""
    print("\n" + "=" * 60)
    print("测试代理参数生成")
    print("=" * 60)
    
    city_mapping, all_cities = test_city_mapping()
    
    print("\n🔧 代理参数生成测试:")
    print("-" * 40)
    
    # 测试几个典型城市
    test_cities = [
        "洛杉矶, 加利福尼亚州",
        "纽约, 纽约州", 
        "芝加哥, 伊利诺伊州",
        "休斯顿, 得克萨斯州",
        "迈阿密, 佛罗里达州"
    ]
    
    for city_display in test_cities:
        if city_display in city_mapping:
            city_en = city_mapping[city_display]
            proxy_param = generate_city_proxy_param(city_display, city_mapping)
            
            print(f"中文显示: {city_display}")
            print(f"英文名称: {city_en}")
            print(f"代理参数: {proxy_param}")
            print(f"完整用户名: customer-z123456_bYIud-{proxy_param}")
            print("-" * 40)

def test_search_functionality():
    """测试搜索功能"""
    print("\n" + "=" * 60)
    print("测试搜索功能")
    print("=" * 60)
    
    city_mapping, all_cities = test_city_mapping()
    
    # 测试搜索关键词
    search_terms = ["洛杉矶", "纽约", "加利福尼亚", "州"]
    
    for term in search_terms:
        print(f"\n🔍 搜索关键词: '{term}'")
        print("-" * 30)
        
        matches = []
        for city in all_cities:
            if term.lower() in city.lower():
                matches.append(city)
        
        if matches:
            for i, match in enumerate(matches, 1):
                print(f"{i}. {match}")
            print(f"找到 {len(matches)} 个匹配结果")
        else:
            print("未找到匹配结果")

def generate_oxylabs_config():
    """生成Oxylabs配置示例"""
    print("\n" + "=" * 60)
    print("Oxylabs配置示例")
    print("=" * 60)
    
    username = "z123456_bYIud"
    password = "Zzr442859970~"
    proxy_host = "pr.oxylabs.io"
    proxy_port = "7777"
    
    print(f"账户: {username}")
    print(f"密码: {'*' * len(password)}")
    print(f"代理服务器: {proxy_host}:{proxy_port}")
    
    # 示例城市配置
    example_cities = [
        ("洛杉矶", "Los Angeles", "cc-US-city-los_angeles"),
        ("纽约", "New York", "cc-US-city-new_york"),
        ("芝加哥", "Chicago", "cc-US-city-chicago"),
        ("休斯顿", "Houston", "cc-US-city-houston")
    ]
    
    print(f"\n🌐 代理配置示例:")
    print("-" * 40)
    
    for city_cn, city_en, city_param in example_cities:
        full_username = f"customer-{username}-{city_param}"
        proxy_url = f"http://{full_username}:{password}@{proxy_host}:{proxy_port}"
        
        print(f"城市: {city_cn} ({city_en})")
        print(f"参数: {city_param}")
        print(f"用户名: {full_username}")
        print(f"代理URL: {proxy_url}")
        print("-" * 40)

def main():
    """主函数"""
    try:
        # 测试城市映射
        test_city_mapping()
        
        # 测试代理参数生成
        test_proxy_generation()
        
        # 测试搜索功能
        test_search_functionality()
        
        # 生成配置示例
        generate_oxylabs_config()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成")
        print("=" * 60)
        print("中文界面功能正常，可以启动GUI工具进行测试！")
        print("\n启动命令:")
        print("python 比特浏览器美国城市代理工具.py")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()
    
    input("\n按回车键退出...")

if __name__ == "__main__":
    main()
